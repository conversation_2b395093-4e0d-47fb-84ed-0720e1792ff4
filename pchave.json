{"PagSeguro": ["p<PERSON><PERSON><PERSON><PERSON>", "pag seguro"], "Mercado Pago": ["mercado pago", "mercadopago", "mercadopago.js", "proxy-mercadopago-v1", "mercadopago-v1", "proxy-mercadopago"], "Vtex": ["vtex.render-server", "vtex.render-server@8.174.1", "vtexassets", "io.vtex.com.br", "vtex.render-runtime", "vtex.device-detector", "vtex.checkout", "vtex-render-session", "vtex.css", "vtex.pixel-manager", "vtex.structured-data", "vtex.sticky-layout", "vtex.react-vtexid", "vtex.store-icons", "vtex.react-portal", "vtex.auth-challenge", "header_cart_vtex.js", "cart_vtex.js", "vtex.js"], "Cielo": ["cielo", "cielo ecommerce", "cielo api", "cielo-webservice", ".woocommerce-checkout #cielo_payment_form"], "Stone": ["stone", "stone co", "stone pagamentos"], "Braspag": ["braspag", "braspag api", "bra<PERSON><PERSON> pagamento"], "GetNet": ["getnet", "getnet api", "getnet pagamentos"], "PayPal": ["paypal", "paypal api"], "Wirecard": ["wirecard", "moip", "moip pagamentos"], "Pagar.me": ["pagar.me", "https://assets.pagar.me/js/pagarme.min.js", "assets.pagar.me/js/pagarme.min.js", "pagarme.min.js", "pagar.me/js", "assets.pagar.me"], "Adyen": ["adyen", "adyen payments", "/pagamentos-adyen"], "Ebanx": ["ebanx", "ebanx pagamentos"], "Rede": ["redecard", "rede api"], "SafraPay": ["safrapay", "safrapay api", "safrapay pagamentos"], "SumUp": ["sumup", "sumup api"], "Bin": ["bin pagamentos"], "Credisis": ["credisis", "credisis api", "credisis pagamentos"], "Vindi": ["vindi", "vindi api", "vindi pagamentos", "ch-icon-vindi", "icon-vindi", "traycheckout\":{\"name\":\"<PERSON><PERSON>", "site\":\"https://www.vindi.com.br/", "www.vindi.com.br", "protegido <PERSON>", "vindiFingerPrintId", "Boleto - Vindi"], "Iugu": ["iugu", "iugu api", "iugu pagamentos", "js.iugu.com"], "PicPay": ["picpay", "picpay api", "picpay pagamentos"], "Zoop": ["zoop", "zoop pagamentos"], "Mundipagg": ["mundipagg", "mundipagg api"], "TrayCheckout": ["data-checkout-template=\"traycheckout", "traycheckout", "traycheckout.com.br/js/finger_print.js"], "PayMee": ["paymee", "paymee pagamentos"], "Gerencianet": ["gerencianet", "gerencianet api", "gerencianet pagamentos"], "EloPagamentos": ["elopagamentos", "elo pagamentos", "elo api"], "Woocommerce": ["woocommerce", "woo-commerce"], "Braintree": ["braintree"], "Stripe": ["stripe", "js.stripe.com"], "Square": ["square"], "Authorize.net": ["authorize.net", "authorizenet"], "Amazon Pay": ["amazon pay", "amazonpay"], "Alipay": ["alipay"], "WeChat Pay": ["wechat pay", "wechatpay"], "Google Pay": ["google pay", "googlepay"], "Apple Pay": ["apple pay", "applepay"], "2Checkout": ["2checkout"], "BlueSnap": ["bluesnap"], "FastSpring": ["fastspring"], "Klarna": ["klarna"], "Paddle": ["paddle"], "Recurly": ["recurly"], "Skrill": ["skrill"], "Fortumo": ["fortumo"], "G2A Pay": ["g2a pay", "g2apay"]}