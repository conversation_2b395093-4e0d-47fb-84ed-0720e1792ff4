package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

func main() {
	// Verificar se a pasta fixed existe
	if _, err := os.Stat("fixed"); os.IsNotExist(err) {
		log.Fatalf("Erro: pasta 'fixed' não encontrada")
	}

	searchTerm := "easydentalcloud.com.br"
	fmt.Printf("Buscando por '%s' em todos os arquivos .txt na pasta 'fixed'...\n", searchTerm)

	// Buscar pelo termo
	count, results, err := searchInFolder("fixed", searchTerm)
	if err != nil {
		log.Fatalf("Erro: %v", err)
	}

	fmt.Printf("Encontradas %d ocorrências do termo '%s'\n", count, searchTerm)
	
	// Salvar resultados em um arquivo
	if count > 0 {
		outFile, err := os.Create("resultados_busca.txt")
		if err != nil {
			log.Fatalf("Erro ao criar arquivo de saída: %v", err)
		}
		defer outFile.Close()
		
		writer := bufio.NewWriter(outFile)
		defer writer.Flush()
		
		for _, line := range results {
			writer.WriteString(line + "\n")
		}
		
		fmt.Println("Resultados salvos em: resultados_busca.txt")
	}
}

// searchInFolder busca por um termo em todos os arquivos .txt em uma pasta
func searchInFolder(folderPath, searchTerm string) (int, []string, error) {
	// Encontrar todos os arquivos .txt na pasta
	txtFiles, err := filepath.Glob(filepath.Join(folderPath, "*.txt"))
	if err != nil {
		return 0, nil, fmt.Errorf("erro ao buscar arquivos .txt: %v", err)
	}
	
	if len(txtFiles) == 0 {
		return 0, nil, fmt.Errorf("nenhum arquivo .txt encontrado na pasta %s", folderPath)
	}
	
	totalCount := 0
	var results []string
	
	// Processar cada arquivo
	for fileIndex, filePath := range txtFiles {
		fmt.Printf("Processando arquivo %d/%d: %s\n", fileIndex+1, len(txtFiles), filePath)
		
		// Abrir arquivo de entrada
		inFile, err := os.Open(filePath)
		if err != nil {
			return totalCount, results, fmt.Errorf("erro ao abrir arquivo %s: %v", filePath, err)
		}
		defer inFile.Close()
		
		scanner := bufio.NewScanner(inFile)
		
		// Aumentar o buffer do scanner para lidar com linhas muito longas
		const maxScanTokenSize = 1024 * 1024 // 1MB
		buf := make([]byte, maxScanTokenSize)
		scanner.Buffer(buf, maxScanTokenSize)
		
		// Contador para mostrar progresso
		lineCount := 0
		lastProgressTime := time.Now()
		progressInterval := 5 * time.Second
		startTime := time.Now()
	
		// Processar cada linha
		for scanner.Scan() {
			line := scanner.Text()
			lineCount++
			
			// Mostrar progresso periodicamente
			if time.Since(lastProgressTime) > progressInterval {
				elapsed := time.Since(startTime)
				linesPerSecond := float64(lineCount) / elapsed.Seconds()
				fmt.Printf("[%s] Processadas %d linhas (%.1f linhas/s), encontradas %d ocorrências...\n", 
					filePath, lineCount, linesPerSecond, totalCount)
				lastProgressTime = time.Now()
			}
	
			// Verificar se a linha contém o termo de busca (case insensitive)
			if strings.Contains(strings.ToLower(line), strings.ToLower(searchTerm)) {
				totalCount++
				results = append(results, line)
				fmt.Printf("Encontrado: %s\n", line)
			}
		}
		
		// Verificar erros do scanner
		if err := scanner.Err(); err != nil {
			return totalCount, results, fmt.Errorf("erro ao ler arquivo %s: %v", filePath, err)
		}
		
		fmt.Printf("Arquivo %s processado. Encontradas %d ocorrências até agora.\n", filePath, totalCount)
	}

	return totalCount, results, nil
}
