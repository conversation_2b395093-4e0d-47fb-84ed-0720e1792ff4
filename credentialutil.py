#!/usr/bin/env python3
import argparse
import concurrent.futures
import os
import re
import sys
import time
import threading
from typing import List, Tuple

# Constantes (mesmas do searchutil.go)
GENERAL_LINES_FILE = "fixed/general_lines.txt"
OUTPUT_FOLDER = "search_results"
MAX_LINE_LENGTH = 1024 * 1024  # 1MB max line length
BUFFER_SIZE = 1024 * 1024  # 1MB buffer size
CHUNK_SIZE = 100000  # Número de linhas por chunk

# Compilar regex para CPF (11 dígitos)
CPF_REGEX = re.compile(r'\b\d{11}\b')

# Compilar regex para extrair apenas dígitos
DIGITS_ONLY = re.compile(r'\d+')

class ChunkResult:
    """Armazena os resultados de uma busca em chunk"""
    def __init__(self):
        self.count = 0
        self.lines = []
        self.total_lines = 0

def ensure_output_folder():
    """Garante que a pasta de saída existe"""
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)

def process_chunk(chunk_data: List[str], chunk_id: int) -> ChunkResult:
    """Processa um chunk de linhas buscando credenciais"""
    result = ChunkResult()
    result.total_lines = len(chunk_data)

    for line in chunk_data:
        # Verificar se a linha contém um CPF (11 dígitos)
        cpf_matches = CPF_REGEX.findall(line)

        if cpf_matches:
            for cpf in cpf_matches:
                # Procurar por senha após o CPF
                parts = line.split(cpf, 1)
                if len(parts) > 1:
                    after_cpf = parts[1]

                    # Extrair apenas os dígitos da parte após o CPF
                    password_digits = DIGITS_ONLY.findall(after_cpf)

                    if password_digits:
                        # Juntar todos os dígitos da senha
                        all_digits = ''.join(password_digits)

                        # Pegar os primeiros 6 dígitos da senha ou preencher com zeros
                        if len(all_digits) > 6:
                            final_password = all_digits[:6]
                        else:
                            final_password = all_digits.zfill(6)

                        # Adicionar a credencial ao resultado
                        credential = f"{cpf}:{final_password}"
                        result.lines.append(credential)
                        result.count += 1

    return result

def read_file_in_chunks(file_path: str, output_file: str) -> Tuple[int, int]:
    """Lê um arquivo em chunks e processa em paralelo, salvando resultados em tempo real"""
    if not os.path.exists(file_path):
        print(f"Erro: arquivo {file_path} não encontrado")
        sys.exit(1)

    # Determinar o número de workers com base nos núcleos disponíveis
    num_workers = os.cpu_count() or 4

    # Ler o arquivo e dividir em chunks
    chunks = []
    current_chunk = []
    total_lines = 0

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                current_chunk.append(line.strip())
                total_lines += 1

                if len(current_chunk) >= CHUNK_SIZE:
                    chunks.append(current_chunk)
                    current_chunk = []

        # Adicionar o último chunk se não estiver vazio
        if current_chunk:
            chunks.append(current_chunk)

    except Exception as e:
        print(f"Erro ao ler arquivo: {e}")
        sys.exit(1)

    print(f"Arquivo dividido em {len(chunks)} chunks")

    # Criar arquivo de saída
    try:
        # Abrir o arquivo em modo de escrita
        output_file_handle = open(output_file, 'w')
    except Exception as e:
        print(f"Erro ao criar arquivo de saída: {e}")
        sys.exit(1)

    # Conjunto para rastrear credenciais já encontradas (evitar duplicatas)
    seen_credentials = set()

    # Lock para acesso concorrente ao arquivo e ao conjunto
    file_lock = threading.Lock()

    # Contador de credenciais encontradas
    total_found = 0

    # Função para salvar credenciais em tempo real
    def save_credentials(credentials):
        nonlocal total_found
        with file_lock:
            for credential in credentials:
                if credential not in seen_credentials:
                    seen_credentials.add(credential)
                    output_file_handle.write(f"{credential}\n")
                    output_file_handle.flush()  # Força a escrita no disco
                    total_found += 1

    # Processar chunks em paralelo
    with concurrent.futures.ProcessPoolExecutor(max_workers=num_workers) as executor:
        future_to_chunk = {executor.submit(process_chunk, chunk, i): i for i, chunk in enumerate(chunks)}

        for future in concurrent.futures.as_completed(future_to_chunk):
            chunk_id = future_to_chunk[future]
            try:
                result = future.result()

                # Salvar credenciais em tempo real
                save_credentials(result.lines)

                print(f"Chunk {chunk_id+1}/{len(chunks)} processado: {result.count} credenciais encontradas")

            except Exception as e:
                print(f"Erro ao processar chunk {chunk_id}: {e}")

    # Fechar o arquivo de saída
    output_file_handle.close()

    return total_found, total_lines

def extract_credentials() -> Tuple[str, int, int]:
    """Extrai credenciais no formato CPF:senha (11 dígitos:6 dígitos)"""
    # Nome do arquivo de saída
    filename = f"{OUTPUT_FOLDER}/credenciais_cpf_senha.txt"

    # Extrair credenciais e salvar em tempo real
    total_found, total_lines = read_file_in_chunks(GENERAL_LINES_FILE, filename)

    return filename, total_found, total_lines

def main():
    """Função principal"""
    # Criar pasta de resultados
    ensure_output_folder()

    # Verificar argumentos de linha de comando
    parser = argparse.ArgumentParser(description='Utilitário para extrair credenciais no formato CPF:senha')
    parser.add_argument('command', choices=['extract'], help='Comando a executar')

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(0)

    args = parser.parse_args()

    if args.command == 'extract':
        print("Extraindo credenciais no formato CPF:senha (11 dígitos:6 dígitos)...")
        start_time = time.time()

        filename, count, total_lines = extract_credentials()

        elapsed = time.time() - start_time
        print(f"Encontradas {count} credenciais únicas em {total_lines} linhas processadas ({elapsed:.2f}s)")
        print(f"Resultados salvos em: {filename}")

if __name__ == "__main__":
    main()
