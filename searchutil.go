package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"
)

const (
	generalLinesFile = "fixed/general_lines.txt"
	outputFolder     = "search_results"
	maxLineLength    = 1024 * 1024 // 1MB max line length
	bufferSize       = 1024 * 1024 // 1MB buffer size
	chunkSize        = 100000      // Número de linhas por chunk
)

type SearchRequest struct {
	Terms        []string `json:"terms"`
	ResponseType string   `json:"response_type"` // "text" ou "file"
}

type SearchResponse struct {
	Success    bool   `json:"success"`
	File       string `json:"file,omitempty"`
	Content    string `json:"content,omitempty"`
	Error      string `json:"error,omitempty"`
	Count      int    `json:"count,omitempty"`
	Time       string `json:"time,omitempty"`
	TotalLines int    `json:"total_lines,omitempty"`
}

// Chunk representa um pedaço do arquivo
type Chunk struct {
	Lines  []string
	Offset int64
}

// SearchResult armazena os resultados de uma busca em chunk
type SearchResult struct {
	Count      int
	Lines      []string
	ChunkNum   int
	TotalLines int // Total de linhas processadas neste chunk
	Error      error
}

var (
	// Usamos um pool de chunks para reutilizar memória
	chunkPool = sync.Pool{
		New: func() interface{} {
			return &Chunk{
				Lines: make([]string, 0, chunkSize),
			}
		},
	}
)

func main() {
	// Criar pasta de resultados
	os.MkdirAll(outputFolder, 0755)

	// Verificar argumentos de linha de comando
	if len(os.Args) > 1 {
		command := os.Args[1]

		switch command {
		case "deduplicate":
			// Remover duplicatas
			fmt.Println("Removendo linhas duplicadas...")
			startTime := time.Now()
			removed, err := deduplicateFile()
			if err != nil {
				log.Fatalf("Erro: %v", err)
			}
			elapsed := time.Since(startTime)
			fmt.Printf("Removidas %d linhas duplicadas em %v\n", removed, elapsed)

		case "search":
			// Pesquisar por termos
			if len(os.Args) < 3 {
				fmt.Println("Uso: searchutil search <termo1> [termo2] [termo3] ...")
				os.Exit(1)
			}

			terms := os.Args[2:]
			fmt.Printf("Buscando por: %s\n", strings.Join(terms, ", "))

			startTime := time.Now()
			filename, count, totalLines, err := searchTerms(terms)
			if err != nil {
				log.Fatalf("Erro: %v", err)
			}

			elapsed := time.Since(startTime)
			fmt.Printf("Encontradas %d linhas com os termos de busca em %d linhas processadas (%v)\n",
				count, totalLines, elapsed)
			fmt.Printf("Resultados salvos em: %s\n", filename)

		case "server":
			// Iniciar servidor HTTP
			startServer()

		default:
			fmt.Println("Comando desconhecido. Uso:")
			fmt.Println("  searchutil deduplicate - Remove linhas duplicadas")
			fmt.Println("  searchutil search <termo1> [termo2] ... - Busca por termos")
			fmt.Println("  searchutil server - Inicia o servidor HTTP")
		}

		return
	}

	// Se nenhum comando for especificado, mostrar ajuda
	fmt.Println("Uso:")
	fmt.Println("  searchutil deduplicate - Remove linhas duplicadas")
	fmt.Println("  searchutil search <termo1> [termo2] ... - Busca por termos")
	fmt.Println("  searchutil server - Inicia o servidor HTTP")
}

func startServer() {
	// Registrar handlers
	http.HandleFunc("/api/search", handleSearchAPI)
	http.HandleFunc("/api/deduplicate", handleDeduplicateAPI)

	// Iniciar servidor
	addr := ":8080"
	log.Printf("Servidor iniciado em %s", addr)
	log.Println("Endpoints disponíveis:")
	log.Println("  POST /api/search - Buscar termos (response_type: 'text' ou 'file')")
	log.Println("  POST /api/deduplicate - Remover duplicatas de general_lines.txt")

	if err := http.ListenAndServe(addr, nil); err != nil {
		log.Fatalf("Erro ao iniciar servidor: %v", err)
	}
}

// ReadFileInChunks lê um arquivo em chunks e executa uma função para cada chunk
func ReadFileInChunks(filePath string, processor func(*Chunk) error) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("erro ao abrir arquivo: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	buf := make([]byte, maxLineLength)
	scanner.Buffer(buf, maxLineLength)

	chunk := chunkPool.Get().(*Chunk)
	chunk.Lines = chunk.Lines[:0] // Reset
	chunkCount := 0

	for scanner.Scan() {
		chunk.Lines = append(chunk.Lines, scanner.Text())

		// Quando o chunk estiver cheio, processá-lo
		if len(chunk.Lines) >= chunkSize {
			if err := processor(chunk); err != nil {
				return err
			}

			// Preparar para o próximo chunk
			chunk = chunkPool.Get().(*Chunk)
			chunk.Lines = chunk.Lines[:0] // Reset
			chunkCount++
		}
	}

	// Processar o último chunk se não estiver vazio
	if len(chunk.Lines) > 0 {
		if err := processor(chunk); err != nil {
			return err
		}
		chunkCount++
	}

	// Verificar erros do scanner
	if err := scanner.Err(); err != nil {
		if strings.Contains(err.Error(), "token too long") {
			log.Printf("Aviso: algumas linhas foram ignoradas por serem muito longas")
		} else {
			return fmt.Errorf("erro ao ler arquivo: %v", err)
		}
	}

	log.Printf("Arquivo processado em %d chunks", chunkCount)
	return nil
}

// ParallelReadFileInChunks lê e processa um arquivo em chunks paralelos
func ParallelReadFileInChunks(filePath string, processor func(*Chunk, int) (interface{}, error)) ([]interface{}, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir arquivo: %v", err)
	}
	defer file.Close()

	// Determinar o número de workers com base nos núcleos disponíveis
	numWorkers := runtime.NumCPU()
	results := make([]interface{}, 0)
	resultsMutex := sync.Mutex{}

	// Canais para comunicação
	chunks := make(chan *Chunk, numWorkers)
	done := make(chan bool)
	errChan := make(chan error, 1)

	// Iniciar workers
	var wg sync.WaitGroup
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for chunk := range chunks {
				result, err := processor(chunk, workerID)
				if err != nil {
					select {
					case errChan <- err:
					default:
					}
					return
				}

				resultsMutex.Lock()
				results = append(results, result)
				resultsMutex.Unlock()

				// Devolver chunk ao pool
				chunkPool.Put(chunk)
			}
		}(i)
	}

	// Iniciar goroutine para fechamento quando todos os workers terminarem
	go func() {
		wg.Wait()
		close(done)
	}()

	// Ler arquivo e enviar chunks para os workers
	scanner := bufio.NewScanner(file)
	buf := make([]byte, maxLineLength)
	scanner.Buffer(buf, maxLineLength)

	chunkNum := 0
	chunk := chunkPool.Get().(*Chunk)
	chunk.Lines = chunk.Lines[:0] // Reset

	for scanner.Scan() {
		chunk.Lines = append(chunk.Lines, scanner.Text())

		// Quando o chunk estiver cheio, enviá-lo para processamento
		if len(chunk.Lines) >= chunkSize {
			select {
			case chunks <- chunk:
				// Preparar próximo chunk
				chunk = chunkPool.Get().(*Chunk)
				chunk.Lines = chunk.Lines[:0]
				chunkNum++
			case err := <-errChan:
				close(chunks)
				return nil, err
			case <-done:
				close(chunks)
				return results, nil
			}
		}
	}

	// Enviar último chunk se não estiver vazio
	if len(chunk.Lines) > 0 {
		select {
		case chunks <- chunk:
			chunkNum++
		case err := <-errChan:
			close(chunks)
			return nil, err
		case <-done:
			close(chunks)
			return results, nil
		}
	}

	// Verificar erros do scanner
	if err := scanner.Err(); err != nil {
		if strings.Contains(err.Error(), "token too long") {
			log.Printf("Aviso: algumas linhas foram ignoradas por serem muito longas")
		} else {
			close(chunks)
			return nil, fmt.Errorf("erro ao ler arquivo: %v", err)
		}
	}

	// Fechar canal de chunks e aguardar finalização dos workers
	close(chunks)

	select {
	case err := <-errChan:
		return nil, err
	case <-done:
		log.Printf("Arquivo processado em %d chunks com %d workers", chunkNum, numWorkers)
		return results, nil
	}
}

func handleSearchAPI(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Método não permitido", http.StatusMethodNotAllowed)
		return
	}

	var req SearchRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Corpo da requisição inválido", http.StatusBadRequest)
		return
	}

	if len(req.Terms) == 0 {
		http.Error(w, "Nenhum termo de busca fornecido", http.StatusBadRequest)
		return
	}

	// Pesquisar e salvar resultados
	startTime := time.Now()
	filename, count, totalLines, err := searchTerms(req.Terms)
	elapsed := time.Since(startTime)

	if err != nil {
		json.NewEncoder(w).Encode(SearchResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Se o tipo de resposta for "file", retorna o arquivo para download
	if req.ResponseType == "file" {
		file, err := os.Open(filename)
		if err != nil {
			json.NewEncoder(w).Encode(SearchResponse{
				Success: false,
				Error:   fmt.Sprintf("Erro ao abrir arquivo: %v", err),
			})
			return
		}
		defer file.Close()

		// Configurar headers para download
		w.Header().Set("Content-Type", "text/plain")
		w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filepath.Base(filename)))

		// Copiar conteúdo do arquivo para a resposta
		io.Copy(w, file)
		return
	}

	// Se o tipo de resposta for "text" ou não especificado, retorna o conteúdo como texto
	content, err := os.ReadFile(filename)
	if err != nil {
		json.NewEncoder(w).Encode(SearchResponse{
			Success: false,
			Error:   fmt.Sprintf("Erro ao ler arquivo: %v", err),
		})
		return
	}

	json.NewEncoder(w).Encode(SearchResponse{
		Success:    true,
		File:       filename,
		Content:    string(content),
		Count:      count,
		Time:       elapsed.String(),
		TotalLines: totalLines,
	})
}

func handleDeduplicateAPI(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Método não permitido", http.StatusMethodNotAllowed)
		return
	}

	startTime := time.Now()
	count, err := deduplicateFile()
	elapsed := time.Since(startTime)

	if err != nil {
		json.NewEncoder(w).Encode(SearchResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	json.NewEncoder(w).Encode(SearchResponse{
		Success: true,
		File:    generalLinesFile,
		Count:   count,
		Time:    elapsed.String(),
	})
}

func searchTerms(terms []string) (string, int, int, error) {
	// Criar nome do arquivo baseado nos termos
	filename := fmt.Sprintf("%s/search_%s.txt", outputFolder, strings.Join(terms, "_"))

	// Abrir arquivo de saída
	outFile, err := os.Create(filename)
	if err != nil {
		return "", 0, 0, fmt.Errorf("erro ao criar arquivo de saída: %v", err)
	}
	defer outFile.Close()

	writer := bufio.NewWriterSize(outFile, bufferSize)
	defer writer.Flush()

	// Preparar termos de busca (lowercase)
	lowerTerms := make([]string, len(terms))
	for i, term := range terms {
		lowerTerms[i] = strings.ToLower(term)
	}

	// Processamento paralelo dos chunks
	totalCount := 0
	totalLinesProcessed := 0

	// Função de processamento para cada chunk
	processChunk := func(chunk *Chunk, workerID int) (interface{}, error) {
		result := &SearchResult{
			ChunkNum:   workerID,
			Lines:      make([]string, 0, chunkSize/10), // Estimativa para 10% de matches
			TotalLines: len(chunk.Lines),                // Total de linhas neste chunk
		}

		for _, line := range chunk.Lines {
			matches := true
			lowerLine := strings.ToLower(line)

			for _, term := range lowerTerms {
				if !strings.Contains(lowerLine, term) {
					matches = false
					break
				}
			}

			if matches {
				result.Lines = append(result.Lines, line)
				result.Count++
			}
		}

		return result, nil
	}

	// Executar busca em paralelo
	results, err := ParallelReadFileInChunks(generalLinesFile, processChunk)
	if err != nil {
		return "", 0, 0, fmt.Errorf("erro durante busca: %v", err)
	}

	// Escrever resultados no arquivo de saída
	for _, res := range results {
		result := res.(*SearchResult)
		totalCount += result.Count
		totalLinesProcessed += result.TotalLines

		for _, line := range result.Lines {
			if _, err := writer.WriteString(line + "\n"); err != nil {
				return filename, totalCount, totalLinesProcessed, fmt.Errorf("erro ao escrever no arquivo: %v", err)
			}
		}
	}

	return filename, totalCount, totalLinesProcessed, nil
}

func deduplicateFile() (int, error) {
	tempFile := generalLinesFile + ".tmp"
	outFile, err := os.Create(tempFile)
	if err != nil {
		return 0, fmt.Errorf("erro ao criar arquivo temporário: %v", err)
	}
	defer outFile.Close()

	writer := bufio.NewWriterSize(outFile, bufferSize)
	defer writer.Flush()

	seen := make(map[string]struct{})
	totalLines := 0
	uniqueLines := 0

	// Processar cada chunk
	err = ReadFileInChunks(generalLinesFile, func(chunk *Chunk) error {
		for _, line := range chunk.Lines {
			totalLines++

			if _, exists := seen[line]; !exists {
				seen[line] = struct{}{}
				uniqueLines++

				if _, err := writer.WriteString(line + "\n"); err != nil {
					return fmt.Errorf("erro ao escrever linha: %v", err)
				}
			}
		}

		// Devolver chunk ao pool
		chunkPool.Put(chunk)
		return nil
	})

	if err != nil {
		return 0, err
	}

	// Garantir que tudo foi escrito
	if err := writer.Flush(); err != nil {
		return 0, fmt.Errorf("erro ao finalizar escrita: %v", err)
	}

	// Substituir arquivo original pelo arquivo sem duplicatas
	if err := os.Rename(tempFile, generalLinesFile); err != nil {
		return 0, fmt.Errorf("erro ao substituir arquivo original: %v", err)
	}

	return totalLines - uniqueLines, nil
}
