#!/usr/bin/env python3
import re
from collections import Counter
from urllib.parse import urlparse
import sys

def extract_domain(url):
    """Extract the domain name from a URL."""
    try:
        # Parse the URL
        parsed_url = urlparse(url)
        
        # Get the netloc (network location part - usually the domain)
        domain = parsed_url.netloc
        
        # If netloc is empty (for URLs without http/https), try the path
        if not domain and parsed_url.path:
            # Some URLs might be in the format domain.com/path without http://
            domain = parsed_url.path.split('/')[0]
        
        # Remove port number if present
        domain = domain.split(':')[0]
        
        # Remove 'www.' prefix if present
        if domain.startswith('www.'):
            domain = domain[4:]
            
        return domain.lower() if domain else None
    except Exception as e:
        print(f"Error parsing URL {url}: {e}")
        return None

def analyze_urls(file_path, top_n=100):
    """Analyze URLs in a file and return the most common domains."""
    domains = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                url = line.strip()
                domain = extract_domain(url)
                if domain and not domain.startswith(('192.168.', '10.', '127.0.0.1', 'localhost')):
                    domains.append(domain)
        
        # Count domain frequencies
        domain_counts = Counter(domains)
        
        # Get the most common domains
        most_common = domain_counts.most_common(top_n)
        
        return most_common
    except Exception as e:
        print(f"Error analyzing URLs: {e}")
        return []

def main():
    # Default file path
    file_path = "unique_urls.txt"
    
    # Default number of top domains to show
    top_n = 100
    
    # Check if file path is provided as command line argument
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    
    # Check if number of top domains is provided
    if len(sys.argv) > 2:
        try:
            top_n = int(sys.argv[2])
        except ValueError:
            print(f"Invalid number: {sys.argv[2]}. Using default: {top_n}")
    
    print(f"Analyzing URLs in {file_path}...")
    most_common = analyze_urls(file_path, top_n)
    
    # Print results
    if most_common:
        print(f"\nTop {len(most_common)} domains by frequency:")
        print("-" * 50)
        print(f"{'Domain':<40} {'Count':>10}")
        print("-" * 50)
        
        for domain, count in most_common:
            print(f"{domain:<40} {count:>10}")
    else:
        print("No domains found or error occurred.")

if __name__ == "__main__":
    main()
