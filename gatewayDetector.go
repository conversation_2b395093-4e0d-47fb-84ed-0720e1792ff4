package main

import (
	"bufio"
	"context"
	"crypto/md5"
	"crypto/tls"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math/rand"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/fatih/color"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	toolname      = "GateWay SpiderCrawler"
	batchSize     = 1000
	totalURLs     = 1000000
	waitTime      = 20 * time.Second
	maxGoroutines = 500 // Aumentado de 100 para 500
	mongoURI      = "mongodb://localhost:27017"
	dbName        = "crawler"
	collName      = "sites"
)

type CertInfo struct {
	Version                    int      `json:"Version"`
	HandshakeComplete          bool     `json:"HandshakeComplete"`
	DidResume                  bool     `json:"DidResume"`
	CipherSuite                uint16   `json:"CipherSuite"`
	NegotiatedProtocol         string   `json:"NegotiatedProtocol"`
	NegotiatedProtocolIsMutual bool     `json:"NegotiatedProtocolIsMutual"`
	ServerName                 string   `json:"ServerName"`
	PeerCertificates           []string `json:"PeerCertificates"`
}

type SiteInfo struct {
	URL           string            `json:"url"`
	StatusCode    int               `json:"status_code"`
	Title         string            `json:"title"`
	ResponseTime  int64             `json:"response_time"`
	IP            string            `json:"ip"`
	CertInfo      *CertInfo         `json:"cert_info,omitempty"`
	HTML          string            `json:"html"`
	Scripts       []string          `json:"scripts"`
	CDNs          []string          `json:"cdns"`
	Headers       map[string]string `json:"headers"`
	Cookies       []string          `json:"cookies"`
	GatewaysFound []string          `json:"gateways_found"`
	JSFileNames   []string          `json:"js_file_names"`
}

var userAgents = []string{
	"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
	"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",
	"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
	"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36",
	"Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
}

func getRandomUserAgent() string {
	return userAgents[rand.Intn(len(userAgents))]
}

func getRandomIP() string {
	return fmt.Sprintf("%d.%d.%d.%d", rand.Intn(256), rand.Intn(256), rand.Intn(256), rand.Intn(256))
}

func center(text string, space int) string {
	lines := strings.Split(text, "\n")
	centered := make([]string, len(lines))
	for i, line := range lines {
		padding := strings.Repeat(" ", space)
		centered[i] = padding + line
	}
	return strings.Join(centered, "\n")
}

func ui() {
	fmt.Println(center("GateWay SpiderCrawler v2.5", 20))
	fmt.Println(center("это не русский хакер [🧙‍♂️ | ΜVĐ]", 20))
	fmt.Println(center("Telegram @hisnotarussianhacker", 20))
}

func verificarSite(url string, palavrasChave map[string][]string, mongoClient *mongo.Client) {
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "https://" + strings.Split(url, "/")[0]
	}

	startTime := time.Now()

	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		MinVersion:         tls.VersionTLS10,
		MaxVersion:         tls.VersionTLS13,
		CipherSuites: []uint16{
			tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,
			tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
		},
	}

	dialer := &net.Dialer{
		Timeout:   30 * time.Second,
		KeepAlive: 30 * time.Second,
	}

	transport := &http.Transport{
		TLSClientConfig:    tlsConfig,
		DisableCompression: true,
		DialContext:        dialer.DialContext,
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   120 * time.Second,
		// Seguir redirecionamentos (até 10)
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 10 {
				return fmt.Errorf("muitos redirecionamentos")
			}
			return nil
		},
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		color.Red("Erro ao criar requisição para %s: %v", url, err)
		return
	}

	req.Header.Set("User-Agent", getRandomUserAgent())
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "none")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Cache-Control", "max-age=0")
	req.Header.Set("X-Forwarded-For", getRandomIP())

	resp, err := client.Do(req)
	if err != nil {
		color.Red("Erro ao acessar %s: %v", url, err)
		return
	}
	defer resp.Body.Close()

	responseTime := time.Since(startTime)

	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		color.Red("Erro ao ler o corpo da resposta de %s: %v", url, err)
		return
	}

	html := string(bodyBytes)
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		color.Red("Erro ao parsear HTML de %s: %v", url, err)
		return
	}

	title := doc.Find("title").Text()

	var gatewaysEnc []string
	for chave, palavras := range palavrasChave {
		for _, palavra := range palavras {
			if strings.Contains(strings.ToLower(html), strings.ToLower(palavra)) {
				gatewaysEnc = append(gatewaysEnc, chave)
				break
			}
		}
	}

	scripts := extractScripts(doc)
	cdns := extractCDNs(doc)
	jsFileNames := extractJSFileNames(doc)

	headers := make(map[string]string)
	for k, v := range resp.Header {
		headers[k] = strings.Join(v, ", ")
	}

	var cookies []string
	for _, cookie := range resp.Cookies() {
		cookies = append(cookies, cookie.String())
	}

	ip, _ := net.LookupIP(strings.TrimPrefix(strings.TrimPrefix(url, "http://"), "https://"))
	ipString := ""
	if len(ip) > 0 {
		ipString = ip[0].String()
	}

	var certInfo *tls.ConnectionState
	if resp.TLS != nil {
		certInfo = resp.TLS
	}

	// Sanitizar dados para evitar problemas com UTF-8 inválido
	sanitizedTitle := sanitizeUTF8(title)
	sanitizedHTML := sanitizeUTF8(html)

	// Sanitizar scripts
	sanitizedScripts := make([]string, len(scripts))
	for i, script := range scripts {
		sanitizedScripts[i] = sanitizeUTF8(script)
	}

	// Sanitizar CDNs
	sanitizedCDNs := make([]string, len(cdns))
	for i, cdn := range cdns {
		sanitizedCDNs[i] = sanitizeUTF8(cdn)
	}

	// Sanitizar headers
	sanitizedHeaders := make(map[string]string)
	for k, v := range headers {
		sanitizedHeaders[sanitizeUTF8(k)] = sanitizeUTF8(v)
	}

	// Sanitizar cookies
	sanitizedCookies := make([]string, len(cookies))
	for i, cookie := range cookies {
		sanitizedCookies[i] = sanitizeUTF8(cookie)
	}

	// Sanitizar JSFileNames
	sanitizedJSFileNames := make([]string, len(jsFileNames))
	for i, fileName := range jsFileNames {
		sanitizedJSFileNames[i] = sanitizeUTF8(fileName)
	}

	siteInfo := SiteInfo{
		URL:           url,
		StatusCode:    resp.StatusCode,
		Title:         sanitizedTitle,
		ResponseTime:  int64(responseTime),
		IP:            ipString,
		CertInfo:      convertCertInfo(certInfo),
		HTML:          sanitizedHTML,
		Scripts:       sanitizedScripts,
		CDNs:          sanitizedCDNs,
		Headers:       sanitizedHeaders,
		Cookies:       sanitizedCookies,
		GatewaysFound: gatewaysEnc,
		JSFileNames:   sanitizedJSFileNames,
	}

	saveToMongoDB(siteInfo, mongoClient)

	color.Green("%s | Status: %d | Título: %s | Tempo: %v | Gateways: %v", url, resp.StatusCode, title, responseTime, gatewaysEnc)
}

func extractScripts(doc *goquery.Document) []string {
	var scripts []string
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		if src, exists := s.Attr("src"); exists {
			scripts = append(scripts, src)
		}
	})
	return scripts
}

func extractCDNs(doc *goquery.Document) []string {
	var cdns []string
	cdnPatterns := []string{
		"cdn.jsdelivr.net",
		"cdnjs.cloudflare.com",
		"unpkg.com",
		"ajax.googleapis.com",
		"stackpath.bootstrapcdn.com",
	}

	doc.Find("script, link[rel='stylesheet']").Each(func(i int, s *goquery.Selection) {
		src, exists := s.Attr("src")
		if !exists {
			src, exists = s.Attr("href")
		}
		if exists {
			for _, pattern := range cdnPatterns {
				if strings.Contains(src, pattern) {
					cdns = append(cdns, src)
					break
				}
			}
		}
	})
	return cdns
}

func extractJSFileNames(doc *goquery.Document) []string {
	var jsFileNames []string
	doc.Find("script[src]").Each(func(i int, s *goquery.Selection) {
		if src, exists := s.Attr("src"); exists {
			fileName := filepath.Base(src)
			if strings.HasSuffix(fileName, ".js") {
				jsFileNames = append(jsFileNames, fileName)
			}
		}
	})
	return jsFileNames
}

func convertCertInfo(certInfo *tls.ConnectionState) *CertInfo {
	if certInfo == nil {
		return nil
	}

	peerCerts := make([]string, len(certInfo.PeerCertificates))
	for i, cert := range certInfo.PeerCertificates {
		peerCerts[i] = base64.StdEncoding.EncodeToString(cert.Raw)
	}

	return &CertInfo{
		Version:                    int(certInfo.Version),
		HandshakeComplete:          certInfo.HandshakeComplete,
		DidResume:                  certInfo.DidResume,
		CipherSuite:                certInfo.CipherSuite,
		NegotiatedProtocol:         certInfo.NegotiatedProtocol,
		NegotiatedProtocolIsMutual: certInfo.NegotiatedProtocolIsMutual,
		ServerName:                 certInfo.ServerName,
		PeerCertificates:           peerCerts,
	}
}

func saveToMongoDB(siteInfo SiteInfo, client *mongo.Client) {
	collection := client.Database(dbName).Collection(collName)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Criar um filtro para verificar se o documento já existe
	filter := bson.M{"url": siteInfo.URL}

	// Opções para upsert (inserir se não existir, atualizar se existir)
	opts := options.Replace().SetUpsert(true)

	// Realizar a operação de substituição diretamente com o documento
	result, err := collection.ReplaceOne(ctx, filter, siteInfo, opts)
	if err != nil {
		color.Red("Erro ao salvar no MongoDB: %v", err)
		return
	}

	if result.UpsertedCount > 0 {
		color.Green("Documento inserido com sucesso: %s", siteInfo.URL)
	} else if result.ModifiedCount > 0 {
		color.Yellow("Documento atualizado com sucesso: %s", siteInfo.URL)
	} else {
		color.Cyan("Documento já existia e não foi modificado: %s", siteInfo.URL)
	}
}

func md5Hash(s string) string {
	h := md5.Sum([]byte(s))
	return hex.EncodeToString(h[:])
}

// sanitizeUTF8 remove caracteres UTF-8 inválidos de uma string
func sanitizeUTF8(s string) string {
	// Converter para []rune para lidar com caracteres multibyte
	runes := []rune{}

	// Iterar sobre cada rune e verificar se é válido
	for _, r := range s {
		// Verificar se o rune está dentro do intervalo válido
		if r != 0xFFFD && // Caractere de substituição Unicode
			(r <= 0x10FFFF) && // Maior valor Unicode válido
			(r < 0xD800 || r > 0xDFFF) { // Excluir surrogate pairs
			runes = append(runes, r)
		}
	}

	return string(runes)
}

func getUniqueURLsFromFile(filePath string, offset, limit int) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir arquivo %s: %w", filePath, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)

	// Pular linhas até o offset
	for i := 0; i < offset && scanner.Scan(); i++ {
		// Apenas avançando o scanner
	}

	var urls []string
	count := 0

	// Ler até o limite
	for scanner.Scan() && count < limit {
		url := strings.TrimSpace(scanner.Text())
		if url != "" {
			urls = append(urls, url)
			count++
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("erro ao ler arquivo %s: %w", filePath, err)
	}

	return urls, nil
}

func runGatewayDetector() {
	ui()
	color.Green("Iniciando Crawler, aguarde por favor!")

	// Ler arquivo de palavras-chave
	palavrasChaveBytes, err := ioutil.ReadFile("pchave.json")
	if err != nil {
		color.Red("Erro ao ler arquivo pchave.json: %v", err)
		return
	}

	var palavrasChave map[string][]string
	err = json.Unmarshal(palavrasChaveBytes, &palavrasChave)
	if err != nil {
		color.Red("Erro ao fazer parse do JSON: %v", err)
		return
	}

	// Conectar ao MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	mongoClient, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURI))
	if err != nil {
		color.Red("Erro ao conectar ao MongoDB: %v", err)
		return
	}
	defer func() {
		if err = mongoClient.Disconnect(ctx); err != nil {
			color.Red("Erro ao desconectar do MongoDB: %v", err)
		}
	}()

	// Verificar conexão com o MongoDB
	err = mongoClient.Ping(ctx, nil)
	if err != nil {
		color.Red("Erro ao verificar conexão com MongoDB: %v", err)
		return
	}
	color.Green("Conectado ao MongoDB com sucesso!")

	// Arquivo de URLs únicas
	urlFilePath := "single_url.txt"

	// Inicializar contadores
	processedURLs := 0
	offset := 0

	// Processar URLs em lotes
	for processedURLs < totalURLs {
		color.Yellow("Lendo URLs do arquivo (offset: %d)...", offset)
		urls, err := getUniqueURLsFromFile(urlFilePath, offset, batchSize)
		if err != nil {
			color.Red("Erro ao obter URLs do arquivo: %v", err)
			return
		}

		if len(urls) == 0 {
			color.Yellow("Não há mais URLs para processar. Encerrando.")
			break
		}

		color.Cyan("Obtidas %d URLs únicas do arquivo (total processado: %d)", len(urls), processedURLs)

		// Usar goroutines para processar URLs em paralelo
		var wg sync.WaitGroup
		semaphore := make(chan struct{}, maxGoroutines)

		for _, url := range urls {
			wg.Add(1)
			semaphore <- struct{}{}
			go func(url string) {
				defer func() {
					<-semaphore
					wg.Done()
				}()
				verificarSite(url, palavrasChave, mongoClient)
			}(url)
		}

		wg.Wait()

		processedURLs += len(urls)
		offset += batchSize

		if processedURLs < totalURLs {
			color.Yellow("Aguardando %v antes da próxima batch...", waitTime)
			time.Sleep(waitTime)
		}
	}

	color.Green("Crawler concluído! Total de URLs processadas: %d", processedURLs)
}
