package main

import (
	"bufio"
	"container/heap"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"sort"
	"strings"
	"time"
	"unicode/utf8"
)

const (
	chunkSize      = 100000 // Aumentado de 10000 para 100000
	hotFolder      = "hot"
	fixedFolder    = "fixed"
	processedLog   = "processed.json"
	cloudGeralRoot = "../CLOUDGERAL"
	maxRepeats     = 5           // Maximum number of repeated characters
	maxLineLength  = 1024 * 1024 // 1MB max line length
	bufferSize     = 1024 * 1024 // 1MB buffer size
)

// File prefixes to separate
var filePrefixes = map[string]string{
	"android://":    "android",
	"https://t.me/": "telegram",
}

type ProcessedLog struct {
	ProcessedFiles []string `json:"processedFiles"`
}

type Item struct {
	Line  string
	Index int
}

type Heap []*Item

type Stats struct {
	InvalidLines   int64
	DuplicateLines int64
	ProcessedFiles int64
	CurrentFile    string
	LastUpdate     time.Time
	InvalidLog     []InvalidLineInfo
	TotalLines     int64
	StartTime      time.Time
}

type InvalidLineInfo struct {
	Line     string
	Reason   string
	Filename string
}

var stats = &Stats{
	InvalidLog: make([]InvalidLineInfo, 0, 1000),
	StartTime:  time.Now(),
}

func (h Heap) Len() int           { return len(h) }
func (h Heap) Less(i, j int) bool { return h[i].Line < h[j].Line }
func (h Heap) Swap(i, j int)      { h[i], h[j] = h[j], h[i] }

func (h *Heap) Push(x interface{}) {
	*h = append(*h, x.(*Item))
}

func (h *Heap) Pop() interface{} {
	old := *h
	n := len(old)
	item := old[n-1]
	*h = old[0 : n-1]
	return item
}

func main() {
	os.MkdirAll(hotFolder, 0755)
	os.MkdirAll(fixedFolder, 0755)

	log, err := readProcessedLog()
	if err != nil {
		fmt.Printf("Error reading processed log: %v\n", err)
		return
	}

	// Start stats display goroutine
	go displayStats()

	// First, scan for and delete any already processed files
	fmt.Println("Scanning for already processed files...")
	err = filepath.WalkDir(cloudGeralRoot, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() && strings.EqualFold(filepath.Ext(path), ".txt") {
			if isProcessed(path, log) {
				// File was already processed and deleted
				return nil
			}
		}
		return nil
	})

	if err != nil {
		fmt.Printf("Error scanning for processed files: %v\n", err)
	}

	// Now process new files
	fmt.Println("Starting to process new files...")
	err = filepath.WalkDir(cloudGeralRoot, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() && strings.EqualFold(filepath.Ext(path), ".txt") {
			// Check if file is completely downloaded
			if isFileComplete(path) && !isProcessed(path, log) {
				stats.CurrentFile = filepath.Base(path)
				fmt.Printf("\nStarting to process %s...\n", path)

				if err := processFile(path); err != nil {
					fmt.Printf("Error processing file: %v\n", err)
					return err
				}

				log.ProcessedFiles = append(log.ProcessedFiles, path)
				if err := writeProcessedLog(log); err != nil {
					fmt.Printf("Error updating log: %v\n", err)
					return err
				}

				stats.ProcessedFiles++
				stats.CurrentFile = ""

				// Write invalid lines log if we have collected 1000 examples
				if len(stats.InvalidLog) >= 1000 {
					writeInvalidLinesLog()
				}
			}
		}
		return nil
	})

	if err != nil {
		fmt.Printf("Directory walk error: %v\n", err)
	}

	// Write invalid lines log at the end if we haven't written it yet
	if len(stats.InvalidLog) > 0 {
		writeInvalidLinesLog()
	}
}

func displayStats() {
	for {
		time.Sleep(time.Second)
		elapsed := time.Since(stats.StartTime).Minutes()
		linesPerMinute := float64(stats.TotalLines) / elapsed
		fmt.Printf("\r\033[KFiles: %d | Invalid: %d | Duplicate: %d | Total: %d | L/Min: %.2f | Current: %s",
			stats.ProcessedFiles,
			stats.InvalidLines,
			stats.DuplicateLines,
			stats.TotalLines,
			linesPerMinute,
			stats.CurrentFile)
	}
}

func isFileComplete(path string) bool {
	// Reduzido o tempo de espera
	time.Sleep(500 * time.Millisecond)

	// Check if file size is stable
	initialSize, err := getFileSize(path)
	if err != nil {
		return false
	}

	time.Sleep(100 * time.Millisecond)

	finalSize, err := getFileSize(path)
	if err != nil {
		return false
	}

	return initialSize == finalSize
}

func getFileSize(path string) (int64, error) {
	file, err := os.Stat(path)
	if err != nil {
		return 0, err
	}
	return file.Size(), nil
}

func isProcessed(path string, log ProcessedLog) bool {
	for _, p := range log.ProcessedFiles {
		if p == path {
			// Delete the processed file
			if err := os.Remove(path); err != nil {
				fmt.Printf("Error deleting processed file %s: %v\n", path, err)
			} else {
				fmt.Printf("Deleted already processed file: %s\n", path)
			}
			return true
		}
	}
	return false
}

func processFile(filePath string) error {
	chunks, err := splitFile(filePath)
	if err != nil {
		return err
	}

	// Process chunks in parallel
	processedChunks := make([]string, 0, len(chunks))
	chunkChan := make(chan []string, len(chunks))
	errChan := make(chan error, len(chunks))
	semaphore := make(chan struct{}, runtime.NumCPU()) // Limita o número de goroutines

	for _, chunk := range chunks {
		semaphore <- struct{}{} // Acquire semaphore
		go func(chunkPath string) {
			defer func() { <-semaphore }() // Release semaphore
			processed, err := processChunk(chunkPath)
			if err != nil {
				errChan <- err
				return
			}
			chunkChan <- processed
		}(chunk)
	}

	// Collect results
	for i := 0; i < len(chunks); i++ {
		select {
		case err := <-errChan:
			return err
		case processed := <-chunkChan:
			processedChunks = append(processedChunks, processed...)
		}
	}

	mergedFiles, err := mergeChunks(processedChunks)
	if err != nil {
		return err
	}

	if err := mergeWithFixed(mergedFiles); err != nil {
		return err
	}

	cleanup(processedChunks, mergedFiles)
	return os.Remove(filePath)
}

func splitFile(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	buf := make([]byte, maxLineLength)
	scanner.Buffer(buf, maxLineLength)

	var chunks []string
	chunkNum := 0

	for {
		lines := make([]string, 0, chunkSize)
		for len(lines) < chunkSize {
			if !scanner.Scan() {
				break
			}
			line := scanner.Text()
			if len(line) > maxLineLength {
				stats.InvalidLines++
				logInvalidLine(line, "Line too long", filepath.Base(filePath))
				continue
			}
			lines = append(lines, line)
			stats.TotalLines++
		}

		if len(lines) == 0 {
			break
		}

		chunkPath := filepath.Join(hotFolder, fmt.Sprintf("chunk_%d.tmp", chunkNum))
		if err := writeLines(lines, chunkPath); err != nil {
			return nil, err
		}
		chunks = append(chunks, chunkPath)
		chunkNum++
	}

	// Ignore scanner error if it's just about token too long
	if err := scanner.Err(); err != nil && !strings.Contains(err.Error(), "token too long") {
		return chunks, err
	}
	return chunks, nil
}

func isValidLine(line string, filename string) bool {
	// Check UTF-8 validity
	if !utf8.ValidString(line) {
		logInvalidLine(line, "Invalid UTF-8", filename)
		return false
	}

	// Check for repeated characters
	if hasRepeatedChars(line) {
		logInvalidLine(line, "Too many repeated characters", filename)
		return false
	}

	// Check for hexadecimal
	if isHexString(line) {
		logInvalidLine(line, "Hexadecimal string", filename)
		return false
	}

	// Check for lines containing only special characters
	if containsOnlySpecialChars(line) {
		logInvalidLine(line, "Contains only special characters", filename)
		return false
	}

	return true
}

func hasRepeatedChars(line string) bool {
	if len(line) < 2 {
		return false
	}

	currentChar := line[0]
	count := 1

	for i := 1; i < len(line); i++ {
		if line[i] == currentChar {
			count++
			if count > maxRepeats {
				return true
			}
		} else {
			currentChar = line[i]
			count = 1
		}
	}
	return false
}

func isHexString(line string) bool {
	hexPattern := regexp.MustCompile(`^[0-9A-Fa-f]+$`)
	return hexPattern.MatchString(line)
}

func containsOnlySpecialChars(line string) bool {
	if len(line) == 0 {
		return false
	}

	// Padrão de caracteres especiais comuns
	specialChars := regexp.MustCompile(`^[*+()\-_@#$%^&*!]+$`)

	// Se a linha contiver apenas caracteres especiais
	if specialChars.MatchString(line) {
		return true
	}

	// Verifica se a linha contém apenas caracteres especiais e espaços
	onlySpecialAndSpaces := regexp.MustCompile(`^[*+()\-_@#$%^&*!\s]+$`)
	if onlySpecialAndSpaces.MatchString(line) {
		return true
	}

	// Verifica se a linha contém apenas caracteres especiais e números
	onlySpecialAndNumbers := regexp.MustCompile(`^[*+()\-_@#$%^&*!0-9]+$`)
	if onlySpecialAndNumbers.MatchString(line) {
		return true
	}

	return false
}

func getFilePrefix(line string) string {
	if strings.HasPrefix(line, "android://") {
		return "android"
	}
	if strings.HasPrefix(line, "https://t.me/") {
		return "telegram"
	}
	return "general"
}

func processChunk(chunkPath string) ([]string, error) {
	lines, err := readLines(chunkPath)
	if err != nil {
		return nil, err
	}

	// Group lines by prefix
	prefixGroups := make(map[string][]string)
	seen := make(map[string]struct{}) // Usando struct{} para economizar memória

	for _, line := range lines {
		if !isValidLine(line, filepath.Base(chunkPath)) {
			stats.InvalidLines++
			continue
		}
		if _, exists := seen[line]; exists {
			stats.DuplicateLines++
			continue
		}
		seen[line] = struct{}{}

		prefix := getFilePrefix(line)
		prefixGroups[prefix] = append(prefixGroups[prefix], line)
	}

	// If all lines were invalid, return empty slice
	if len(prefixGroups) == 0 {
		return []string{}, nil
	}

	// Process each prefix group
	var processedFiles []string
	for prefix, groupLines := range prefixGroups {
		sort.Strings(groupLines)
		processedPath := chunkPath + "." + prefix + ".processed"
		if err := writeLines(groupLines, processedPath); err != nil {
			return nil, err
		}
		processedFiles = append(processedFiles, processedPath)
	}

	return processedFiles, nil
}

func mergeChunks(chunkPaths []string) (map[string]string, error) {
	if len(chunkPaths) == 0 {
		return map[string]string{}, nil // Return empty map instead of error
	}

	// Group chunks by prefix
	prefixChunks := make(map[string][]string)
	for _, path := range chunkPaths {
		parts := strings.Split(filepath.Base(path), ".")
		if len(parts) < 3 {
			continue // Skip invalid filenames
		}
		prefix := parts[2] // Extract prefix from filename
		prefixChunks[prefix] = append(prefixChunks[prefix], path)
	}

	// If no valid chunks found, return empty map
	if len(prefixChunks) == 0 {
		return map[string]string{}, nil
	}

	// Process each prefix group
	mergedFiles := make(map[string]string)
	for prefix, chunks := range prefixChunks {
		mergedPath, err := mergeChunksForPrefix(chunks)
		if err != nil {
			return nil, err
		}
		mergedFiles[prefix] = mergedPath
	}

	return mergedFiles, nil
}

func mergeChunksForPrefix(chunkPaths []string) (string, error) {
	scanners := make([]*bufio.Scanner, len(chunkPaths))
	for i, path := range chunkPaths {
		file, err := os.Open(path)
		if err != nil {
			return "", err
		}
		defer file.Close()
		scanner := bufio.NewScanner(file)
		buf := make([]byte, maxLineLength)
		scanner.Buffer(buf, maxLineLength)
		scanners[i] = scanner
	}

	h := &Heap{}
	heap.Init(h)

	// Pre-fill heap with first lines
	for i, s := range scanners {
		if s.Scan() {
			line := s.Text()
			if len(line) > maxLineLength {
				stats.InvalidLines++
				logInvalidLine(line, "Line too long", filepath.Base(chunkPaths[i]))
				continue
			}
			heap.Push(h, &Item{Line: line, Index: i})
		}
	}

	prefix := strings.Split(filepath.Base(chunkPaths[0]), ".")[2]
	mergedPath := filepath.Join(hotFolder, "merged_"+prefix+".tmp")
	outFile, err := os.Create(mergedPath)
	if err != nil {
		return "", err
	}
	defer outFile.Close()

	writer := bufio.NewWriterSize(outFile, bufferSize)
	defer writer.Flush()

	var lastLine string
	for h.Len() > 0 {
		item := heap.Pop(h).(*Item)
		if item.Line != lastLine {
			writer.WriteString(item.Line + "\n")
			lastLine = item.Line
		}

		if scanners[item.Index].Scan() {
			line := scanners[item.Index].Text()
			if len(line) > maxLineLength {
				stats.InvalidLines++
				logInvalidLine(line, "Line too long", filepath.Base(chunkPaths[item.Index]))
				continue
			}
			heap.Push(h, &Item{Line: line, Index: item.Index})
		}
	}

	return mergedPath, nil
}

func mergeWithFixed(mergedFiles map[string]string) error {
	for prefix, tempPath := range mergedFiles {
		fixedPath := filepath.Join(fixedFolder, prefix+"_lines.txt")

		if _, err := os.Stat(fixedPath); os.IsNotExist(err) {
			return os.Rename(tempPath, fixedPath)
		}

		tempFile, err := os.Open(tempPath)
		if err != nil {
			return err
		}
		defer tempFile.Close()

		fixedFile, err := os.Open(fixedPath)
		if err != nil {
			return err
		}
		defer fixedFile.Close()

		newFixed, err := os.CreateTemp(hotFolder, "new_fixed_*.tmp")
		if err != nil {
			return err
		}
		defer os.Remove(newFixed.Name())

		tempScanner := bufio.NewScanner(tempFile)
		fixedScanner := bufio.NewScanner(fixedFile)
		buf := make([]byte, maxLineLength)
		tempScanner.Buffer(buf, maxLineLength)
		fixedScanner.Buffer(buf, maxLineLength)

		writer := bufio.NewWriter(newFixed)
		seen := make(map[string]struct{}) // Map para rastrear linhas já vistas

		var tempLine, fixedLine string
		hasTemp := tempScanner.Scan()
		if hasTemp {
			tempLine = tempScanner.Text()
			if len(tempLine) > maxLineLength {
				stats.InvalidLines++
				logInvalidLine(tempLine, "Line too long", filepath.Base(tempPath))
				hasTemp = false
			}
		}
		hasFixed := fixedScanner.Scan()
		if hasFixed {
			fixedLine = fixedScanner.Text()
			if len(fixedLine) > maxLineLength {
				stats.InvalidLines++
				logInvalidLine(fixedLine, "Line too long", filepath.Base(fixedPath))
				hasFixed = false
			}
		}

		for hasTemp || hasFixed {
			var currentLine string

			switch {
			case hasTemp && hasFixed:
				if tempLine < fixedLine {
					currentLine = tempLine
					hasTemp = tempScanner.Scan()
					if hasTemp {
						tempLine = tempScanner.Text()
						if len(tempLine) > maxLineLength {
							stats.InvalidLines++
							logInvalidLine(tempLine, "Line too long", filepath.Base(tempPath))
							hasTemp = false
						}
					}
				} else {
					currentLine = fixedLine
					hasFixed = fixedScanner.Scan()
					if hasFixed {
						fixedLine = fixedScanner.Text()
						if len(fixedLine) > maxLineLength {
							stats.InvalidLines++
							logInvalidLine(fixedLine, "Line too long", filepath.Base(fixedPath))
							hasFixed = false
						}
					}
				}
			case hasTemp:
				currentLine = tempLine
				hasTemp = tempScanner.Scan()
				if hasTemp {
					tempLine = tempScanner.Text()
					if len(tempLine) > maxLineLength {
						stats.InvalidLines++
						logInvalidLine(tempLine, "Line too long", filepath.Base(tempPath))
						hasTemp = false
					}
				}
			default:
				currentLine = fixedLine
				hasFixed = fixedScanner.Scan()
				if hasFixed {
					fixedLine = fixedScanner.Text()
					if len(fixedLine) > maxLineLength {
						stats.InvalidLines++
						logInvalidLine(fixedLine, "Line too long", filepath.Base(fixedPath))
						hasFixed = false
					}
				}
			}

			// Só escreve a linha se ela ainda não foi vista
			if _, exists := seen[currentLine]; !exists {
				writer.WriteString(currentLine + "\n")
				seen[currentLine] = struct{}{}
			} else {
				stats.DuplicateLines++
			}
		}

		writer.Flush()
		newFixed.Close()

		if err := os.Rename(newFixed.Name(), fixedPath); err != nil {
			return err
		}
		os.Remove(tempPath)
	}
	return nil
}

func readLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	buf := make([]byte, maxLineLength)
	scanner.Buffer(buf, maxLineLength)

	var lines []string
	for scanner.Scan() {
		line := scanner.Text()
		if len(line) > maxLineLength {
			stats.InvalidLines++
			logInvalidLine(line, "Line too long", filepath.Base(path))
			continue
		}
		lines = append(lines, line)
	}

	// Ignore scanner error if it's just about token too long
	if err := scanner.Err(); err != nil && !strings.Contains(err.Error(), "token too long") {
		return lines, err
	}
	return lines, nil
}

func writeLines(lines []string, path string) error {
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := bufio.NewWriterSize(file, bufferSize)
	for _, line := range lines {
		_, err := writer.WriteString(line + "\n")
		if err != nil {
			return err
		}
	}
	return writer.Flush()
}

func readProcessedLog() (ProcessedLog, error) {
	var log ProcessedLog

	data, err := os.ReadFile(processedLog)
	if os.IsNotExist(err) {
		return log, nil
	}
	if err != nil {
		return log, err
	}

	err = json.Unmarshal(data, &log)
	return log, err
}

func writeProcessedLog(log ProcessedLog) error {
	data, err := json.MarshalIndent(log, "", "  ")
	if err != nil {
		return err
	}
	return os.WriteFile(processedLog, data, 0644)
}

func cleanup(processedChunks []string, mergedFiles map[string]string) {
	for _, path := range processedChunks {
		os.Remove(path)
	}
	for _, tempPath := range mergedFiles {
		os.Remove(tempPath)
	}
}

func logInvalidLine(line, reason, filename string) {
	if len(stats.InvalidLog) < 1000 {
		stats.InvalidLog = append(stats.InvalidLog, InvalidLineInfo{
			Line:     line,
			Reason:   reason,
			Filename: filename,
		})
	}
}

func writeInvalidLinesLog() error {
	file, err := os.Create("invalid_lines.log")
	if err != nil {
		return err
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	writer.WriteString("Invalid Lines Examples (First 1000):\n")
	writer.WriteString("================================\n\n")

	for _, info := range stats.InvalidLog {
		writer.WriteString(fmt.Sprintf("File: %s\n", info.Filename))
		writer.WriteString(fmt.Sprintf("Reason: %s\n", info.Reason))
		writer.WriteString(fmt.Sprintf("Line: %s\n", info.Line))
		writer.WriteString("--------------------------------\n")
	}

	writer.WriteString(fmt.Sprintf("\nTotal Invalid Lines: %d\n", stats.InvalidLines))
	return nil
}
