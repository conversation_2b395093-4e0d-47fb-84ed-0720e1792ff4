#!/usr/bin/env python3
import re

def extrair_6_digitos(senha):
    # Extrair apenas os dígitos da senha
    digitos = re.sub(r'\D', '', senha)
    # Retornar os primeiros 6 dígitos, se houver pelo menos 6
    if len(digitos) >= 6:
        return digitos[:6]
    # Se tiver menos de 6 dígitos, preencher com zeros à esquerda
    return digitos.zfill(6)

# Ler o arquivo de credenciais
with open('credenciais.txt', 'r') as f:
    linhas = f.readlines()

# Filtrar as credenciais no formato 11digitos:6digitos
credenciais_filtradas = []

for linha in linhas:
    linha = linha.strip()
    if not linha:
        continue

    # Dividir em usuário e senha
    partes = linha.split(':', 1)
    if len(partes) != 2:
        continue

    usuario, senha = partes

    # Verificar se o usuário tem exatamente 11 dígitos (CPF)
    if re.match(r'^\d{11}$', usuario):
        # Extrair os 6 dígitos da senha
        senha_6_digitos = extrair_6_digitos(senha)
        if senha_6_digitos:
            credenciais_filtradas.append(f"{usuario}:{senha_6_digitos}")

# Escrever as credenciais filtradas em um novo arquivo
with open('credenciais_filtradas.txt', 'w') as f:
    f.write('\n'.join(credenciais_filtradas))

print(f"Processamento concluído. Encontradas {len(credenciais_filtradas)} credenciais no formato CPF:6digitos.")
