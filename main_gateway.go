package main

import (
	"fmt"
	"os"
)

func main() {
	// Verificar se o arquivo de URLs existe
	if _, err := os.Stat("single_url.txt"); os.IsNotExist(err) {
		fmt.Println("Erro: O arquivo single_url.txt não foi encontrado.")
		fmt.Println("Crie um arquivo single_url.txt com a URL que deseja analisar.")
		return
	}

	// Verificar se o MongoDB está em execução
	// Nota: Esta é uma verificação simplificada. Na prática, você pode querer verificar
	// se o MongoDB está realmente em execução antes de iniciar o detector.
	fmt.Println("Verificando se o MongoDB está em execução...")
	fmt.Println("Certifique-se de que o MongoDB está rodando na porta padrão (27017).")
	fmt.Println("Se não estiver, ajuste a constante mongoURI no arquivo gatewayDetector.go.")

	// Executar o detector de gateways
	fmt.Println("Iniciando o detector de gateways...")
	runGatewayDetector()
}
