package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"time"
)

// CredentialResult armazena os resultados de uma busca de credenciais
type CredentialResult struct {
	Count      int
	Lines      []string
	TotalLines int
}

func main() {
	// Verificar se a pasta fixed existe
	if _, err := os.Stat("fixed"); os.IsNotExist(err) {
		log.Fatalf("Erro: pasta 'fixed' não encontrada")
	}

	fmt.Println("Extraindo credenciais no formato CPF:senha (11 dígitos:6 dígitos) de todos os arquivos .txt na pasta 'fixed'...")

	// Extrair credenciais
	count, err := extractCredentialsFromFolder("fixed")
	if err != nil {
		log.Fatalf("Erro: %v", err)
	}

	fmt.Printf("Encontradas %d credenciais no formato CPF:senha\n", count)
	fmt.Println("Resultados salvos em: credenciais_filtradas.txt")
}

// extractCredentialsFromFolder extrai credenciais de todos os arquivos .txt em uma pasta
func extractCredentialsFromFolder(folderPath string) (int, error) {
	// Abrir arquivo de saída
	outFile, err := os.Create("credenciais_filtradas.txt")
	if err != nil {
		return 0, fmt.Errorf("erro ao criar arquivo de saída: %v", err)
	}
	defer outFile.Close()

	writer := bufio.NewWriter(outFile)
	defer writer.Flush()

	// Compilar regex para CPF (11 dígitos)
	cpfRegex := regexp.MustCompile(`^\d{11}$`)

	// Compilar regex para extrair apenas dígitos
	digitsOnly := regexp.MustCompile(`\d+`)

	totalCount := 0
	uniqueCredentials := make(map[string]struct{})
	
	// Configurações para o garbage collector
	const batchSize = 100000 // Processar em lotes de 100.000 credenciais
	batchCount := 0
	lastGCTime := time.Now()
	gcInterval := 30 * time.Second // Forçar GC a cada 30 segundos
	
	// Encontrar todos os arquivos .txt na pasta
	txtFiles, err := filepath.Glob(filepath.Join(folderPath, "*.txt"))
	if err != nil {
		return 0, fmt.Errorf("erro ao buscar arquivos .txt: %v", err)
	}
	
	if len(txtFiles) == 0 {
		return 0, fmt.Errorf("nenhum arquivo .txt encontrado na pasta %s", folderPath)
	}
	
	// Processar cada arquivo
	for fileIndex, filePath := range txtFiles {
		fmt.Printf("Processando arquivo %d/%d: %s\n", fileIndex+1, len(txtFiles), filePath)
		
		// Abrir arquivo de entrada
		inFile, err := os.Open(filePath)
		if err != nil {
			return totalCount, fmt.Errorf("erro ao abrir arquivo %s: %v", filePath, err)
		}
		
		scanner := bufio.NewScanner(inFile)
		
		// Aumentar o buffer do scanner para lidar com linhas muito longas
		const maxScanTokenSize = 1024 * 1024 // 1MB
		buf := make([]byte, maxScanTokenSize)
		scanner.Buffer(buf, maxScanTokenSize)
		
		// Contador para mostrar progresso
		lineCount := 0
		lastProgressTime := time.Now()
		progressInterval := 5 * time.Second
		startTime := time.Now()
	
		// Processar cada linha
		for scanner.Scan() {
			line := scanner.Text()
			lineCount++
			
			// Mostrar progresso periodicamente
			if time.Since(lastProgressTime) > progressInterval {
				elapsed := time.Since(startTime)
				linesPerSecond := float64(lineCount) / elapsed.Seconds()
				fmt.Printf("[%s] Processadas %d linhas (%.1f linhas/s), encontradas %d credenciais únicas...\n", 
					filePath, lineCount, linesPerSecond, totalCount)
				lastProgressTime = time.Now()
			}
	
			// Dividir a linha em usuário e senha
			parts := strings.Split(line, ":")
			if len(parts) != 2 {
				continue
			}
	
			username := parts[0]
			password := parts[1]
	
			// Verificar se o username é um CPF (11 dígitos)
			if !cpfRegex.MatchString(username) {
				continue
			}
	
			// Extrair apenas os dígitos da senha
			passwordDigits := digitsOnly.FindAllString(password, -1)
			if len(passwordDigits) == 0 {
				continue
			}
	
			// Juntar todos os dígitos da senha
			allDigits := strings.Join(passwordDigits, "")
	
			// Pegar os primeiros 6 dígitos da senha ou preencher com zeros
			finalPassword := ""
			if len(allDigits) > 6 {
				finalPassword = allDigits[:6]
			} else {
				finalPassword = fmt.Sprintf("%0*s", 6, allDigits)
			}
	
			// Criar a credencial final
			credential := fmt.Sprintf("%s:%s", username, finalPassword)
	
			// Adicionar apenas se não for duplicada
			if _, exists := uniqueCredentials[credential]; !exists {
				uniqueCredentials[credential] = struct{}{}
				totalCount++
				batchCount++
			}
			
			// Verificar se é hora de fazer garbage collection
			if batchCount >= batchSize || time.Since(lastGCTime) > gcInterval {
				// Escrever credenciais no arquivo
				for cred := range uniqueCredentials {
					if _, err := writer.WriteString(cred + "\n"); err != nil {
						inFile.Close()
						return totalCount, fmt.Errorf("erro ao escrever no arquivo: %v", err)
					}
				}
				
				// Forçar escrita no disco
				if err := writer.Flush(); err != nil {
					inFile.Close()
					return totalCount, fmt.Errorf("erro ao flush do buffer: %v", err)
				}
				
				// Limpar o mapa para liberar memória
				uniqueCredentials = make(map[string]struct{})
				
				// Forçar garbage collection
				runtime.GC()
				
				// Atualizar contadores
				batchCount = 0
				lastGCTime = time.Now()
				
				memStats := runtime.MemStats{}
				runtime.ReadMemStats(&memStats)
				
				fmt.Printf("Garbage collection executado. Memória em uso: %.2f MB. Total de %d credenciais processadas.\n", 
					float64(memStats.Alloc)/1024/1024, totalCount)
			}
		}
		
		// Verificar erros do scanner
		if err := scanner.Err(); err != nil {
			inFile.Close()
			return totalCount, fmt.Errorf("erro ao ler arquivo %s: %v", filePath, err)
		}
		
		inFile.Close()
		fmt.Printf("Arquivo %s processado. Encontradas %d credenciais únicas até agora.\n", filePath, totalCount)
	}
	
	// Escrever as credenciais restantes
	for cred := range uniqueCredentials {
		if _, err := writer.WriteString(cred + "\n"); err != nil {
			return totalCount, fmt.Errorf("erro ao escrever no arquivo: %v", err)
		}
	}

	return totalCount, nil
}
