#!/usr/bin/env python3
import re
import sys

def extract_credentials(input_file, output_file):
    """
    Extrai credenciais no formato CPF:senha (11 dígitos:6 dígitos) de um arquivo.
    Remove caracteres não numéricos das senhas e mantém apenas os 6 primeiros dígitos.
    """
    # Compilar regex para CPF (11 dígitos)
    cpf_regex = re.compile(r'^\d{11}$')
    
    # Compilar regex para extrair apenas dígitos
    digits_only = re.compile(r'\d+')
    
    # Conjunto para evitar duplicatas
    unique_credentials = set()
    
    # Ler arquivo de entrada
    try:
        with open(input_file, 'r') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Erro ao abrir arquivo de entrada: {e}")
        return 0
    
    # Processar cada linha
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # Dividir a linha em usuário e senha
        parts = line.split(':', 1)
        if len(parts) != 2:
            continue
        
        username, password = parts
        
        # Verificar se o username é um CPF (11 dígitos)
        if not cpf_regex.match(username):
            continue
        
        # Extrair apenas os dígitos da senha
        password_digits = ''.join(digits_only.findall(password))
        
        # Pegar os primeiros 6 dígitos da senha ou preencher com zeros
        if len(password_digits) > 6:
            final_password = password_digits[:6]
        else:
            final_password = password_digits.zfill(6)
        
        # Criar a credencial final
        credential = f"{username}:{final_password}"
        
        # Adicionar apenas se não for duplicada
        unique_credentials.add(credential)
    
    # Escrever credenciais no arquivo de saída
    try:
        with open(output_file, 'w') as f:
            for credential in sorted(unique_credentials):
                f.write(f"{credential}\n")
    except Exception as e:
        print(f"Erro ao escrever no arquivo de saída: {e}")
        return 0
    
    return len(unique_credentials)

if __name__ == "__main__":
    input_file = "credenciais.txt"
    output_file = "credenciais_filtradas.txt"
    
    print(f"Extraindo credenciais no formato CPF:senha (11 dígitos:6 dígitos)...")
    count = extract_credentials(input_file, output_file)
    print(f"Encontradas {count} credenciais no formato CPF:senha")
    print(f"Resultados salvos em: {output_file}")
