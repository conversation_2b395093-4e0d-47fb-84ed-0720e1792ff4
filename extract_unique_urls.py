#!/usr/bin/env python3
import re
from pathlib import Path

def extract_urls_from_file(file_path):
    """Extract URLs from a file, reading it as bytes for efficiency."""
    try:
        with open(file_path, 'rb') as f:
            content = f.read().decode('utf-8', errors='ignore')

            # Regex pattern to match URLs
            # This pattern matches http/https URLs
            url_pattern = re.compile(r'https?://[^\s:]+(?:\.[^\s:]+)+(?:/[^\s:]*)?', re.IGNORECASE)

            # Find all URLs in the content
            urls = url_pattern.findall(content)

            # Also look for URLs that might be in the format domain.com/path without http/https
            domain_pattern = re.compile(r'(?<![:/a-zA-Z0-9])([a-zA-Z0-9][-a-zA-Z0-9]*\.)+[a-zA-Z]{2,}(?:\/[^\s:]*)?', re.IGNORECASE)
            domain_urls = domain_pattern.findall(content)

            # Clean up URLs (remove trailing characters that might not be part of the URL)
            cleaned_urls = []

            # Process http/https URLs
            for url in urls:
                # Remove trailing characters that are not typically part of URLs
                url = re.sub(r'[,;"\')]$', '', url)
                cleaned_urls.append(url)

            # Process domain URLs (add http:// prefix)
            for domain in domain_urls:
                # Remove trailing characters that are not typically part of URLs
                domain = re.sub(r'[,;"\')]$', '', domain)
                # Only add domains that look legitimate (have at least one dot and no spaces)
                if '.' in domain and ' ' not in domain:
                    # Don't add if it's already in our list (as part of a full URL)
                    if not any(domain in url for url in cleaned_urls):
                        cleaned_urls.append(f"http://{domain}")

            return cleaned_urls
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def is_valid_url(url):
    """Check if a URL is valid."""
    # Filter out URLs that are likely invalid
    invalid_patterns = [
        r'^http://\.$',           # http://.
        r'^http://0\.$',          # http://0.
        r'^http://\.[^/]+$',      # http://.domain
        r'^http://[0-9]+\.$',     # http://123.
        r'^http://[0-9\.]+$',     # http://0.0.0.0
    ]

    for pattern in invalid_patterns:
        if re.match(pattern, url):
            return False

    # Check if the URL has a valid domain structure
    domain_part = url.split('://', 1)[-1].split('/', 1)[0]

    # Valid domain should have at least one dot and contain valid characters
    if '.' not in domain_part or ' ' in domain_part:
        return False

    # Domain should not start or end with a dot
    if domain_part.startswith('.') or domain_part.endswith('.'):
        return False

    return True

def main():
    # Path to the search_results directory
    search_dir = Path("search_results")

    # Set to store unique URLs
    unique_urls = set()

    # Output file path
    output_file = "unique_urls.txt"

    # Count of processed files
    processed_files = 0

    # Process each file in the directory
    for file_path in search_dir.glob("*.txt"):
        urls = extract_urls_from_file(file_path)
        unique_urls.update(urls)
        processed_files += 1

        # Print progress every 10 files
        if processed_files % 10 == 0:
            print(f"Processed {processed_files} files. Found {len(unique_urls)} unique URLs so far.")

    # Filter valid URLs
    valid_urls = [url for url in unique_urls if is_valid_url(url)]

    # Save unique URLs to output file
    with open(output_file, 'w') as f:
        for url in sorted(valid_urls):
            f.write(f"{url}\n")

    print(f"Completed! Processed {processed_files} files.")
    print(f"Found {len(unique_urls)} unique URLs, {len(valid_urls)} of which are valid.")
    print(f"Results saved to {output_file}")

if __name__ == "__main__":
    main()
