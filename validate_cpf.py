#!/usr/bin/env python3

def validate_cpf(cpf):
    """
    Validates a CPF number according to the Brazilian algorithm.

    Args:
        cpf (str): A string containing only the digits of the CPF.

    Returns:
        bool: True if the CPF is valid, False otherwise.
    """
    # CPF must be 11 digits
    if len(cpf) != 11:
        return False

    # All same digits are invalid (e.g., 11111111111)
    if len(set(cpf)) == 1:
        return False

    # Calculate first verification digit
    sum_of_products = 0
    for i in range(9):
        sum_of_products += int(cpf[i]) * (10 - i)

    remainder = sum_of_products % 11
    digit1 = 0 if remainder < 2 else 11 - remainder

    if str(digit1) != cpf[9]:
        return False

    # Calculate second verification digit
    sum_of_products = 0
    for i in range(10):
        sum_of_products += int(cpf[i]) * (11 - i)

    remainder = sum_of_products % 11
    digit2 = 0 if remainder < 2 else 11 - remainder

    return str(digit2) == cpf[10]

def main():
    input_file = "search_results/cpfSenha.txt"
    output_file = "search_results/cpfSenha_valid.txt"

    valid_entries = []
    total_entries = 0
    invalid_entries = 0

    try:
        with open(input_file, 'r') as f:
            for line in f:
                total_entries += 1
                line = line.strip()
                if not line:
                    continue

                parts = line.split(':')
                if len(parts) != 2:
                    print(f"Malformed line: {line}")
                    continue

                cpf, password = parts

                # Remove any non-digit characters from CPF
                cpf_digits = ''.join(c for c in cpf if c.isdigit())

                if validate_cpf(cpf_digits):
                    valid_entries.append(f"{cpf}:{password}")
                else:
                    invalid_entries += 1
                    if invalid_entries <= 10:  # Only print the first 10 invalid CPFs
                        print(f"Invalid CPF: {cpf}")

        # Write valid entries to output file
        with open(output_file, 'w') as f:
            for entry in valid_entries:
                f.write(f"{entry}\n")

        print(f"Total entries processed: {total_entries}")
        print(f"Valid CPF entries: {len(valid_entries)}")
        print(f"Invalid CPF entries: {invalid_entries}")
        print(f"Valid entries saved to {output_file}")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
